<?php

/**
 * Employees Controller
 * Handles employee CRUD operations
 */

/**
 * Handle employee form submission
 */
function handleEmployeesForm(array $data, Database $db): array
{
    $action = $data['action'] ?? '';
    $id = $data['id'] ?? '';

    if ($action === 'save') {
        // Validate CSRF token
        if (!Application::verifyCsrfToken($data['csrf_token'] ?? '')) {
            return ['success' => false, 'error' => 'Invalid request token'];
        }

        // Build working hours
        $workingHours = [];
        $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        $warnings = [];

        foreach ($days as $day) {
            if (isset($data['working_days']) && in_array($day, $data['working_days'])) {
                $periods = [];

                // Handle multiple periods (arrays)
                $starts = $data["{$day}_start"] ?? [];
                $ends = $data["{$day}_end"] ?? [];

                // Ensure arrays
                if (!is_array($starts)) $starts = [$starts];
                if (!is_array($ends)) $ends = [$ends];

                // Build periods from start/end pairs
                for ($i = 0; $i < count($starts) && $i < count($ends); $i++) {
                    if (!empty($starts[$i]) && !empty($ends[$i])) {
                        $periods[] = [
                            'start' => $starts[$i],
                            'end' => $ends[$i]
                        ];
                    }
                }

                if (!empty($periods)) {
                    // Validate employee hours against store hours
                    $dayOfWeek = array_search($day, ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']);
                    $storeHours = $db->fetchRow(
                        "SELECT start_time, end_time, is_active FROM working_hours WHERE day_of_week = :day",
                        [':day' => $dayOfWeek]
                    );

                    if ($storeHours && $storeHours['is_active']) {
                        $storeStart = $storeHours['start_time'];
                        $storeEnd = $storeHours['end_time'];

                        foreach ($periods as $period) {
                            if ($period['start'] < $storeStart || $period['end'] > $storeEnd) {
                                $warnings[] = ucfirst($day) . ": Employee hours ({$period['start']}-{$period['end']}) extend beyond store hours ($storeStart-$storeEnd)";
                            }
                        }
                    } elseif (!$storeHours || !$storeHours['is_active']) {
                        $warnings[] = ucfirst($day) . ": Store is closed on this day, but employee is scheduled to work";
                    }

                    $workingHours[$day] = $periods;
                } else {
                    $workingHours[$day] = [];
                }
            } else {
                $workingHours[$day] = [];
            }
        }

        $employeeData = [
            ':name' => Application::sanitize($data['name']),
            ':name_en' => Application::sanitize($data['name_en'] ?? ''),
            ':email' => Application::sanitize($data['email']),
            ':phone' => Application::sanitize($data['phone']),
            ':position' => Application::sanitize($data['position']),
            ':color' => Application::sanitize($data['color']),
            ':working_hours' => json_encode($workingHours),
            ':sort_order' => isset($data['sort_order']) ? (int)$data['sort_order'] : 0,
            ':is_active' => isset($data['is_active']) ? 1 : 0,
            ':updated_at' => date('Y-m-d H:i:s')
        ];

        try {
            $db->beginTransaction();

            if ($id) {
                // Update existing employee
                $employeeData[':id'] = $id;
                $sql = "UPDATE employees SET
                        name = :name,
                        name_en = :name_en,
                        email = :email,
                        phone = :phone,
                        position = :position,
                        color = :color,
                        working_hours = :working_hours,
                        sort_order = :sort_order,
                        is_active = :is_active,
                        updated_at = :updated_at
                        WHERE id = :id";
                $employeeId = $id;
            } else {
                // Create new employee
                $employeeId = Application::generateId('EMP');
                $employeeData[':id'] = $employeeId;
                $employeeData[':created_at'] = date('Y-m-d H:i:s');
                $sql = "INSERT INTO employees (id, name, name_en, email, phone, position, color, working_hours, sort_order, is_active, created_at, updated_at)
                        VALUES (:id, :name, :name_en, :email, :phone, :position, :color, :working_hours, :sort_order, :is_active, :created_at, :updated_at)";
            }

            $result = $db->query($sql, $employeeData);

            if ($result !== false) {
                // Handle service assignments
                // First, remove existing service assignments
                $db->query("DELETE FROM employee_services WHERE employee_id = :employee_id", [':employee_id' => $employeeId]);

                // Add new service assignments
                if (!empty($data['service_ids'])) {
                    foreach ($data['service_ids'] as $serviceId) {
                        $db->query(
                            "INSERT INTO employee_services (employee_id, service_id) VALUES (:employee_id, :service_id)",
                            [':employee_id' => $employeeId, ':service_id' => $serviceId]
                        );
                    }
                }

                $db->commit();

                $message = $id ? 'Employee updated successfully' : 'Employee created successfully';
                if (!empty($warnings)) {
                    $message .= ' (Warnings: ' . implode('; ', $warnings) . ')';
                }

                return [
                    'success' => true,
                    'redirect' => '/store-admin/?page=employees',
                    'message' => $message,
                    'warnings' => $warnings
                ];
            } else {
                $db->rollback();
                return ['success' => false, 'error' => 'Failed to save employee'];
            }
        } catch (Exception $e) {
            $db->rollback();
            return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
        }
    }

    return ['success' => false, 'error' => 'Invalid action'];
}

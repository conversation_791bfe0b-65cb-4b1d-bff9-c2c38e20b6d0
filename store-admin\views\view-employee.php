<?php

/**
 * View Employee Page - Compact Design
 * Display employee details with icon-based information and improved layout
 */

// Get employee ID from URL
$employeeId = $_GET['id'] ?? '';

if (!$employeeId) {
    Application::redirect('/store-admin/?page=employees', 'Employee not found', 'error');
    exit;
}

// Check if SMS is enabled
$smsEnabled = Application::getSetting('sms_enabled', '0') === '1';

// Fetch employee data
try {
    $employee = $db->fetchRow("
        SELECT e.*, 
               COUNT(r.id) as total_appointments,
               COUNT(CASE WHEN r.status = 'completed' THEN 1 END) as completed_appointments,
               SUM(CASE WHEN r.status = 'completed' THEN s.price ELSE 0 END) as total_revenue
        FROM employees e
        LEFT JOIN reservations r ON e.id = r.employee_id
        LEFT JOIN services s ON r.service_id = s.id
        WHERE e.id = :id
        GROUP BY e.id
    ", [':id' => $employeeId]);

    if (!$employee) {
        Application::redirect('/store-admin/?page=employees', 'Employee not found', 'error');
        exit;
    }

    // Get recent appointments
    $recentAppointments = $db->fetchAll("
        SELECT r.*, c.name as customer_name, s.name as service_name
        FROM reservations r
        LEFT JOIN customers c ON r.customer_id = c.id
        LEFT JOIN services s ON r.service_id = s.id
        WHERE r.employee_id = :id
        ORDER BY r.date DESC, r.start_time DESC
        LIMIT 10
    ", [':id' => $employeeId]);

    // Get working hours from employee record
    $workingHoursJson = $employee['working_hours'] ?? '';
    $workingHours = [];
    if (!empty($workingHoursJson)) {
        $workingHoursData = json_decode($workingHoursJson, true);
        if ($workingHoursData) {
            // Convert from the stored format to display format
            foreach ($workingHoursData as $day => $periods) {
                if (!empty($periods)) {
                    foreach ($periods as $period) {
                        $workingHours[] = [
                            'day_of_week' => $day,
                            'start_time' => $period['start'],
                            'end_time' => $period['end']
                        ];
                    }
                }
            }
        }
    }

    // Get assigned services
    $assignedServices = $db->fetchAll("
        SELECT s.id, s.name, s.duration, s.price, c.name as category_name, c.color as category_color
        FROM services s
        INNER JOIN employee_services es ON s.id = es.service_id
        LEFT JOIN categories c ON s.category_id = c.id
        WHERE es.employee_id = :id AND s.is_active = 1
        ORDER BY c.name, s.name
    ", [':id' => $employeeId]);
} catch (Exception $e) {
    Application::redirect('/store-admin/?page=employees', 'Error loading employee data', 'error');
    exit;
}

// Generate avatar color if not set
$avatarColor = $employee['color'] ?? '#' . substr(md5($employee['name']), 0, 6);
?>

<div class="view-page">
    <!-- Header -->
    <div class="view-header">
        <div class="view-header-content">
            <div class="view-breadcrumb">
                <a href="/store-admin/?page=employees" class="breadcrumb-link">
                    <i class="fas fa-users-cog"></i> Employees
                </a>
                <span class="breadcrumb-separator">/</span>
                <span class="breadcrumb-current"><?= htmlspecialchars($employee['name']) ?></span>
            </div>

            <div class="view-title">
                <div class="employee-avatar" style="background-color: <?= htmlspecialchars($avatarColor) ?>">
                    <?php
                    $initial = mb_substr($employee['name'], 0, 1, 'UTF-8');
                    echo $initial ? mb_strtoupper($initial, 'UTF-8') : '<i class="fas fa-user"></i>';
                    ?>
                </div>
                <div class="title-content">
                    <h1><?= htmlspecialchars($employee['name']) ?></h1>
                    <div class="view-subtitle">
                        <?php if (!empty($employee['position'])): ?>
                            <span class="position-badge"><?= htmlspecialchars($employee['position']) ?></span>
                        <?php endif; ?>
                        <span class="status-badge status-<?= $employee['is_active'] ? 'active' : 'inactive' ?>">
                            <i class="fas fa-<?= $employee['is_active'] ? 'check-circle' : 'times-circle' ?>"></i>
                            <?= $employee['is_active'] ? 'Active' : 'Inactive' ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <div class="view-actions">
            <a href="/store-admin/?page=edit-employee&id=<?= htmlspecialchars($employeeId) ?>" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <div class="dropdown">
                <button class="btn btn-outline dropdown-toggle" type="button">
                    <i class="fas fa-ellipsis-v"></i>
                </button>
                <div class="dropdown-menu">
                    <button onclick="toggleEmployeeStatus('<?= htmlspecialchars($employeeId) ?>')" class="dropdown-item">
                        <i class="fas fa-toggle-<?= $employee['is_active'] ? 'on' : 'off' ?>"></i>
                        <?= $employee['is_active'] ? 'Deactivate' : 'Activate' ?>
                    </button>
                    <a href="/store-admin/?page=reservations&employee_id=<?= htmlspecialchars($employeeId) ?>" class="dropdown-item">
                        <i class="fas fa-calendar-alt"></i> View Schedule
                    </a>
                    <a href="/store-admin/?page=services&employee_id=<?= htmlspecialchars($employeeId) ?>" class="dropdown-item">
                        <i class="fas fa-cog"></i> Manage Services
                    </a>
                    <a href="/store-admin/?page=add-reservation&employee_id=<?= htmlspecialchars($employeeId) ?>" class="dropdown-item">
                        <i class="fas fa-calendar-plus"></i> Book Appointment
                    </a>
                    <button onclick="sendNotification('<?= htmlspecialchars($employeeId) ?>')" class="dropdown-item">
                        <i class="fas fa-bell"></i> Send Notification
                    </button>
                    <button onclick="sendSMSToEmployee('<?= htmlspecialchars($employeeId) ?>')"
                        class="dropdown-item <?= !$smsEnabled ? 'disabled' : '' ?>"
                        <?= !$smsEnabled ? 'disabled title="SMS messaging is disabled in settings"' : '' ?>>
                        <i class="fas fa-sms"></i> Send SMS
                    </button>
                    <div class="dropdown-divider"></div>
                    <button onclick="deleteEmployee('<?= htmlspecialchars($employeeId) ?>')" class="dropdown-item text-danger">
                        <i class="fas fa-trash"></i> Delete Employee
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="view-content">
        <div class="view-grid">
            <!-- Employee Information -->
            <div class="view-section">
                <div class="section-header">
                    <h2><i class="fas fa-id-card"></i> Personal Information</h2>
                </div>
                <div class="section-content">
                    <div class="consolidated-info-box">
                        <div class="info-row">
                            <div class="info-item">
                                <i class="fas fa-user info-icon"></i>
                                <span class="info-label">Name:</span>
                                <span class="info-value"><?= htmlspecialchars($employee['name']) ?></span>
                            </div>
                            <?php if (!empty($employee['position'])): ?>
                                <div class="info-item">
                                    <i class="fas fa-briefcase info-icon"></i>
                                    <span class="info-label">Position:</span>
                                    <span class="info-value"><?= htmlspecialchars($employee['position']) ?></span>
                                </div>
                            <?php endif; ?>
                        </div>

                        <?php if (!empty($employee['email']) || !empty($employee['phone'])): ?>
                            <div class="info-row">
                                <?php if (!empty($employee['email'])): ?>
                                    <div class="info-item">
                                        <i class="fas fa-envelope info-icon"></i>
                                        <span class="info-label">Email:</span>
                                        <a href="mailto:<?= htmlspecialchars($employee['email']) ?>" class="info-value contact-link">
                                            <?= htmlspecialchars($employee['email']) ?>
                                        </a>
                                    </div>
                                <?php endif; ?>
                                <?php if (!empty($employee['phone'])): ?>
                                    <div class="info-item">
                                        <i class="fas fa-phone info-icon"></i>
                                        <span class="info-label">Phone:</span>
                                        <a href="tel:<?= htmlspecialchars($employee['phone']) ?>" class="info-value contact-link">
                                            <?= htmlspecialchars($employee['phone']) ?>
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>

                        <div class="info-row">
                            <div class="info-item">
                                <i class="fas fa-palette info-icon"></i>
                                <span class="info-label">Color:</span>
                                <div class="info-value">
                                    <div class="color-preview" style="background-color: <?= htmlspecialchars($avatarColor) ?>; display: inline-block; width: 20px; height: 20px; border-radius: 4px; margin-right: 8px; vertical-align: middle;"></div>
                                    <?= htmlspecialchars($avatarColor) ?>
                                </div>
                            </div>
                        </div>

                        <?php if (!empty($employee['notes'])): ?>
                            <div class="info-row full-width">
                                <div class="info-item">
                                    <i class="fas fa-sticky-note info-icon"></i>
                                    <span class="info-label">Notes:</span>
                                    <div class="info-value description"><?= nl2br(htmlspecialchars($employee['notes'])) ?></div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Performance Statistics -->
            <div class="view-section">
                <div class="section-header">
                    <h2><i class="fas fa-chart-line"></i> Performance Statistics</h2>
                </div>
                <div class="section-content">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value"><?= $employee['total_appointments'] ?></div>
                            <div class="stat-label">Total Appointments</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value"><?= $employee['completed_appointments'] ?></div>
                            <div class="stat-label">Completed</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">€<?= number_format($employee['total_revenue'] ?? 0, 2) ?></div>
                            <div class="stat-label">Total Revenue</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">
                                <?= $employee['total_appointments'] > 0 ? round(($employee['completed_appointments'] / $employee['total_appointments']) * 100, 1) : 0 ?>%
                            </div>
                            <div class="stat-label">Completion Rate</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Working Hours -->
        <div class="view-section">
            <div class="section-header">
                <h2><i class="fas fa-clock"></i> Working Hours</h2>
            </div>
            <div class="section-content">
                <?php if (!empty($workingHours)): ?>
                    <div class="working-hours-list">
                        <?php
                        $groupedHours = [];
                        foreach ($workingHours as $hour) {
                            $groupedHours[$hour['day_of_week']][] = $hour;
                        }

                        $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
                        foreach ($days as $day):
                            if (isset($groupedHours[$day])):
                        ?>
                                <div class="working-day">
                                    <div class="day-name"><?= ucfirst($day) ?></div>
                                    <div class="day-hours">
                                        <?php foreach ($groupedHours[$day] as $period): ?>
                                            <span class="time-period">
                                                <?= date('H:i', strtotime($period['start_time'])) ?> -
                                                <?= date('H:i', strtotime($period['end_time'])) ?>
                                            </span>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                        <?php
                            endif;
                        endforeach;
                        ?>
                    </div>
                <?php else: ?>
                    <div class="info-item-with-icon">
                        <div class="info-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">Working Hours</div>
                            <div class="info-value">No working hours set</div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Assigned Services -->
        <div class="view-section">
            <div class="section-header">
                <h2><i class="fas fa-cog"></i> Assigned Services</h2>
                <a href="/store-admin/?page=edit-employee&id=<?= htmlspecialchars($employeeId) ?>" class="btn btn-outline btn-sm">
                    <i class="fas fa-edit"></i> Manage Services
                </a>
            </div>
            <div class="section-content">
                <?php if (!empty($assignedServices)): ?>
                    <div class="services-list">
                        <?php foreach ($assignedServices as $service): ?>
                            <div class="service-item">
                                <div class="service-name"><?= htmlspecialchars($service['name']) ?></div>
                                <div class="service-details">
                                    <span><?= $service['duration'] ?> min</span>
                                    <span>€<?= number_format($service['price'], 2) ?></span>
                                    <?php if ($service['category_name']): ?>
                                        <span class="category-badge" style="background-color: <?= htmlspecialchars($service['category_color']) ?>">
                                            <?= htmlspecialchars($service['category_name']) ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="info-item-with-icon">
                        <div class="info-icon">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">Services</div>
                            <div class="info-value">No services assigned</div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>



        <!-- Recent Appointments -->
        <?php if (!empty($recentAppointments)): ?>
            <div class="view-section">
                <div class="section-header">
                    <h2><i class="fas fa-calendar-check"></i> Recent Appointments</h2>
                    <a href="/store-admin/?page=reservations&employee_id=<?= htmlspecialchars($employeeId) ?>" class="btn btn-outline btn-sm">
                        <i class="fas fa-external-link-alt"></i> View All
                    </a>
                </div>
                <div class="section-content">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Date & Time</th>
                                    <th>Customer</th>
                                    <th>Service</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentAppointments as $appointment): ?>
                                    <tr>
                                        <td>
                                            <div class="date-info">
                                                <strong><?= date('M j, Y', strtotime($appointment['date'])) ?></strong><br>
                                                <small><?= date('H:i', strtotime($appointment['start_time'])) ?></small>
                                            </div>
                                        </td>
                                        <td><?= htmlspecialchars($appointment['customer_name']) ?></td>
                                        <td><?= htmlspecialchars($appointment['service_name']) ?></td>
                                        <td>
                                            <span class="status-badge status-<?= $appointment['status'] ?>">
                                                <?= ucfirst($appointment['status']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <a href="/store-admin/?page=view-reservation&id=<?= $appointment['id'] ?>"
                                                class="btn btn-sm btn-outline" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
    function toggleEmployeeStatus(id) {
        const formData = new FormData();
        formData.append('action', 'toggle_employee_status');
        formData.append('id', id);

        fetch('/store-admin/controllers/ajax.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Employee status updated successfully', 'success');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showNotification('Error updating employee status: ' + (data.error || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                showNotification('Network error occurred', 'error');
            });
    }

    function deleteEmployee(id) {
        if (confirm('Are you sure you want to delete this employee? This action cannot be undone.')) {
            const formData = new FormData();
            formData.append('action', 'delete_employee');
            formData.append('id', id);

            fetch('/store-admin/controllers/ajax.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('Employee deleted successfully', 'success');
                        setTimeout(() => {
                            window.location.href = '/store-admin/?page=employees';
                        }, 1000);
                    } else {
                        showNotification('Error deleting employee: ' + (data.error || 'Unknown error'), 'error');
                    }
                })
                .catch(error => {
                    showNotification('Network error occurred', 'error');
                });
        }
    }

    function sendMessage(employeeId) {
        showNotification('Message feature coming soon', 'info');
    }
</script>
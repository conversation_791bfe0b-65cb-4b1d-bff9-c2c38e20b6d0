<?php

/**
 * Email Templates Controller
 * Handle email template management operations
 */

require_once __DIR__ . '/../core/Application.php';

/**
 * Handle email templates form submission
 */
function handleEmailTemplatesForm(array $data, Database $db): array
{
    try {
        $action = $data['action'] ?? '';

        if ($action === 'save_email_templates') {
            return saveEmailTemplates($data, $db);
        }

        return ['success' => false, 'error' => 'Invalid action'];
    } catch (Exception $e) {
        error_log("Email templates controller error: " . $e->getMessage());
        return ['success' => false, 'error' => 'An error occurred while processing the request'];
    }
}

/**
 * Save email templates
 */
function saveEmailTemplates(array $data, Database $db): array
{
    try {
        $templateType = $data['template_type'] ?? '';

        if (!in_array($templateType, ['verification', 'booking_confirmation'])) {
            return ['success' => false, 'error' => 'Invalid template type'];
        }

        // Ensure email_templates table exists
        createEmailTemplatesTable($db);

        // Get enabled status
        $enabledKey = $templateType . '_enabled';
        $isEnabled = isset($data[$enabledKey]) ? 1 : 0;

        // Start transaction
        $db->beginTransaction();

        // Save templates for both languages
        $languages = ['el', 'en'];
        $savedCount = 0;

        foreach ($languages as $language) {
            $subjectKey = $templateType . '_subject_' . $language;
            $contentKey = $templateType . '_content_' . $language;

            $subject = Application::sanitize($data[$subjectKey] ?? '');
            $content = $data[$contentKey] ?? ''; // Don't sanitize HTML content

            // Skip if both subject and content are empty (allow partial saves)
            if (empty($subject) && empty($content)) {
                continue;
            }

            // Require both subject and content if one is provided
            if (empty($subject) || empty($content)) {
                throw new Exception("Both subject and content are required for {$language} template");
            }

            // Check if template exists
            $existingTemplate = $db->fetchRow(
                "SELECT id FROM email_templates WHERE template_type = :type AND language = :lang",
                [':type' => $templateType, ':lang' => $language]
            );

            if ($existingTemplate) {
                // Update existing template
                $db->query(
                    "UPDATE email_templates SET subject = :subject, content = :content, is_enabled = :enabled, updated_at = :updated
                     WHERE template_type = :type AND language = :lang",
                    [
                        ':subject' => $subject,
                        ':content' => $content,
                        ':enabled' => $isEnabled,
                        ':updated' => date('Y-m-d H:i:s'),
                        ':type' => $templateType,
                        ':lang' => $language
                    ]
                );
            } else {
                // Create new template
                $db->query(
                    "INSERT INTO email_templates (id, template_type, language, subject, content, is_enabled, created_at, updated_at)
                     VALUES (:id, :type, :lang, :subject, :content, :enabled, :created, :updated)",
                    [
                        ':id' => Application::generateId('TPL'),
                        ':type' => $templateType,
                        ':lang' => $language,
                        ':subject' => $subject,
                        ':content' => $content,
                        ':enabled' => $isEnabled,
                        ':created' => date('Y-m-d H:i:s'),
                        ':updated' => date('Y-m-d H:i:s')
                    ]
                );
            }

            $savedCount++;
        }

        if ($savedCount === 0) {
            throw new Exception("At least one language template must be provided");
        }

        $db->commit();

        // Debug logging
        error_log("Email templates saved successfully. Saved count: " . $savedCount);

        return [
            'success' => true,
            'message' => 'Email templates saved successfully'
        ];
    } catch (Exception $e) {
        $db->rollback();
        error_log("Save email templates error: " . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Create email_templates table if it doesn't exist
 */
function createEmailTemplatesTable(Database $db): void
{
    try {
        $db->query("
            CREATE TABLE IF NOT EXISTS email_templates (
                id TEXT PRIMARY KEY,
                template_type TEXT NOT NULL,
                language TEXT NOT NULL,
                subject TEXT NOT NULL,
                content TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(template_type, language)
            )
        ");
    } catch (Exception $e) {
        error_log("Failed to create email_templates table: " . $e->getMessage());
        throw $e;
    }
}

/**
 * Get email template from database
 */
function getEmailTemplate(string $templateType, string $language, Database $db): ?array
{
    try {
        return $db->fetchRow(
            "SELECT * FROM email_templates WHERE template_type = :type AND language = :lang",
            [':type' => $templateType, ':lang' => $language]
        );
    } catch (Exception $e) {
        error_log("Get email template error: " . $e->getMessage());
        return null;
    }
}

/**
 * Get all email templates
 */
function getAllEmailTemplates(Database $db): array
{
    try {
        $templates = [];
        $results = $db->fetchAll("SELECT * FROM email_templates ORDER BY template_type, language");

        foreach ($results as $template) {
            $templates[$template['template_type']][$template['language']] = $template;
        }

        return $templates;
    } catch (Exception $e) {
        error_log("Get all email templates error: " . $e->getMessage());
        return [];
    }
}

<?php

/**
 * Services View
 * Manage services
 */

// Get search and pagination parameters
$searchParams = AdminHelpers::getSearchParams();
$search = $searchParams['search'];
$pageNum = $searchParams['page_num'];
$perPage = $searchParams['per_page'];

// Get filter parameters
$categoryId = $_GET['category_id'] ?? '';
$priceMin = $_GET['price_min'] ?? '';
$priceMax = $_GET['price_max'] ?? '';
$durationMin = $_GET['duration_min'] ?? '';
$durationMax = $_GET['duration_max'] ?? '';

// Build search query - search by service name, description, category name, and price
$searchWhere = AdminHelpers::buildSearchWhere($search, ['s.name', 's.description', 'c.name', 's.price']);

// Add filter conditions
$filterConditions = [];
$filterParams = [];

if ($categoryId) {
    $filterConditions[] = "s.category_id = :category_id";
    $filterParams[':category_id'] = $categoryId;
}

if ($priceMin) {
    $filterConditions[] = "s.price >= :price_min";
    $filterParams[':price_min'] = $priceMin;
}

if ($priceMax && $priceMax != '999999') {
    $filterConditions[] = "s.price <= :price_max";
    $filterParams[':price_max'] = $priceMax;
}

if ($durationMin) {
    $filterConditions[] = "s.duration >= :duration_min";
    $filterParams[':duration_min'] = $durationMin;
}

if ($durationMax && $durationMax != '999999') {
    $filterConditions[] = "s.duration <= :duration_max";
    $filterParams[':duration_max'] = $durationMax;
}

// Combine search and filter conditions
$whereClause = $searchWhere['where'];
$allParams = array_merge($searchWhere['params'], $filterParams);

if (!empty($filterConditions)) {
    $filterWhere = ' AND ' . implode(' AND ', $filterConditions);
    if ($whereClause) {
        $whereClause .= $filterWhere;
    } else {
        $whereClause = 'WHERE ' . implode(' AND ', $filterConditions);
    }
}

// Get total count for pagination first
$countSql = "SELECT COUNT(*) as total
             FROM services s
             LEFT JOIN categories c ON s.category_id = c.id
             {$whereClause}";
$totalCount = $db->fetchRow($countSql, $allParams)['total'];

// Create pagination
$pagination = new Pagination($totalCount, $perPage, $pageNum);

// Get services with categories using proper SQL pagination
$sql = "SELECT s.*, c.name as category_name, c.color as category_color
        FROM services s
        LEFT JOIN categories c ON s.category_id = c.id
        {$whereClause}
        ORDER BY s.name ASC
        LIMIT {$perPage} OFFSET {$pagination->getOffset()}";
$paginatedServices = $db->fetchAll($sql, $allParams);
?>

<!-- Main Toolbar -->
<div class="main-toolbar">
    <div class="toolbar-left">
        <a href="/store-admin/?page=add-service" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add Service
        </a>
        <button class="btn btn-secondary" onclick="manageCategories()">
            <i class="fas fa-tags"></i> Categories
        </button>
        <div class="view-toggle">
            <button class="active" data-view="cards">
                <i class="fas fa-th-large"></i> Cards
            </button>
            <button data-view="table">
                <i class="fas fa-list"></i> Table
            </button>
        </div>
    </div>
    <div class="toolbar-right">
        <div class="dropdown">
            <button class="btn btn-primary dropdown-toggle" type="button" data-toggle="dropdown">
                <i class="fas fa-download"></i> Export
            </button>
            <div class="dropdown-menu">
                <a class="dropdown-item" href="#" onclick="exportServices('csv')">
                    <i class="fas fa-file-csv"></i> Export as CSV
                </a>
                <a class="dropdown-item" href="#" onclick="exportServices('xlsx')">
                    <i class="fas fa-file-excel"></i> Export as XLSX
                </a>
            </div>
        </div>
        <div class="dropdown">
            <button class="btn btn-secondary dropdown-toggle" type="button" data-toggle="dropdown">
                <i class="fas fa-upload"></i> Import
            </button>
            <div class="dropdown-menu">
                <a class="dropdown-item" href="#" onclick="downloadServicesTemplate()">
                    <i class="fas fa-download"></i> Download Template
                </a>
                <a class="dropdown-item" href="#" onclick="importServices()">
                    <i class="fas fa-upload"></i> Import XLSX
                </a>
            </div>
        </div>
        <button class="btn btn-secondary" onclick="toggleFilters()" id="filter-toggle-btn">
            <i class="fas fa-filter"></i> Filters
        </button>
        <div class="search-bar">
            <input type="text" placeholder="Search services, categories, prices..." value="<?php echo htmlspecialchars($search); ?>" id="service-search">
            <button type="button" class="search-btn" onclick="performSearch(document.getElementById('service-search').value)">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>
</div>

<!-- Filters Section -->
<div class="filters-section" id="filters-section" style="display: none;">
    <div class="filters-header">
        <h3><i class="fas fa-filter"></i> Filters</h3>
        <button class="btn btn-outline btn-sm" onclick="clearFilters()">
            <i class="fas fa-times"></i> Clear All
        </button>
    </div>
    <div class="filters-content">
        <div class="filter-group">
            <label class="filter-label">Category:</label>
            <select class="form-control" id="category-filter">
                <option value="">All Categories</option>
                <?php
                $categories = $db->fetchAll("SELECT DISTINCT c.id, c.name FROM categories c JOIN services s ON c.id = s.category_id ORDER BY c.name");
                foreach ($categories as $category): ?>
                    <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="filter-group">
            <label class="filter-label">Price Range:</label>
            <select class="form-control" id="price-filter">
                <option value="">All Prices</option>
                <option value="0-25">€0 - €25</option>
                <option value="25-50">€25 - €50</option>
                <option value="50-100">€50 - €100</option>
                <option value="100+">€100+</option>
            </select>
        </div>
        <div class="filter-group">
            <label class="filter-label">Duration:</label>
            <select class="form-control" id="duration-filter">
                <option value="">All Durations</option>
                <option value="0-30">0-30 min</option>
                <option value="30-60">30-60 min</option>
                <option value="60-120">1-2 hours</option>
                <option value="120+">2+ hours</option>
            </select>
        </div>
        <div class="filter-group">
            <label class="filter-label">Status:</label>
            <select class="form-control" id="status-filter">
                <option value="">All Services</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
            </select>
        </div>
    </div>
</div>



<!-- Enhanced Services Grid -->
<div class="entity-grid" id="services-grid">
    <?php if (empty($paginatedServices)): ?>
        <div class="empty-state">
            <i class="fas fa-cut fa-2x text-muted"></i>
            <h3>No services found</h3>
            <p>Start by adding your first service or adjust your search criteria.</p>
            <button class="btn btn-primary btn-lg" onclick="addService()">
                <i class="fas fa-plus"></i> Add First Service
            </button>
        </div>
    <?php else: ?>
        <?php
        // Include the entity card component
        require_once __DIR__ . '/components/entity-card.php';

        foreach ($paginatedServices as $service):
            // Prepare service data for the card component
            $totalDuration = ($service['preparation_time'] ?? 0) + $service['duration'] + ($service['cleanup_time'] ?? 0);

            // Prepare service data for card
            $serviceData = [
                'id' => $service['id'],
                'name' => $service['name'],
                'description' => $service['description'] ?? '',
                'category_name' => $service['category_name'] ?? 'Uncategorized',
                'category_color' => $service['category_color'] ?? '',
                'price' => $service['price'],
                'duration' => $service['duration'],
                'total_duration' => $totalDuration,
                'preparation_time' => $service['preparation_time'] ?? 0,
                'cleanup_time' => $service['cleanup_time'] ?? 0,
                'active' => $service['is_active'] ?? true,
                'created_at' => $service['created_at'] ?? null
            ];

            // Render the service card
            echo renderEntityCard($serviceData, 'service', [
                'show_checkbox' => false,
                'show_actions' => false
            ]);
        endforeach; ?>

        <!-- Add Service Card -->
        <div class="entity-card add-card" onclick="window.location.href='/store-admin/?page=add-service'">
            <div class="add-card-content">
                <i class="fas fa-plus fa-2x"></i>
                <h3>Add New Service</h3>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Pagination -->
<?php if ($pagination->getTotalPages() > 1): ?>
    <div class="pagination-container">
        <?php echo $pagination->render(); ?>
    </div>
<?php endif; ?>

<style>
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
    }

    .page-actions {
        display: flex;
        gap: 10px;
    }

    .card-title-section {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .category-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.85rem;
        font-weight: 500;
        color: white;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .pagination-container {
        margin-top: 30px;
    }

    @media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            gap: 15px;
        }

        .page-actions {
            flex-wrap: wrap;
            justify-content: center;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize filters
        initializeFilters();
    });

    function initializeFilters() {
        // Get URL parameters to restore filter values
        const urlParams = new URLSearchParams(window.location.search);

        // Restore category filter
        const categoryFilter = document.getElementById('category-filter');
        if (categoryFilter && urlParams.get('category_id')) {
            categoryFilter.value = urlParams.get('category_id');
        }

        // Restore price filter
        const priceFilter = document.getElementById('price-filter');
        if (priceFilter) {
            const priceMin = urlParams.get('price_min');
            const priceMax = urlParams.get('price_max');
            if (priceMin && priceMax) {
                if (priceMax === '999999') {
                    priceFilter.value = priceMin + '+';
                } else {
                    priceFilter.value = priceMin + '-' + priceMax;
                }
            }
        }

        // Restore duration filter
        const durationFilter = document.getElementById('duration-filter');
        if (durationFilter) {
            const durationMin = urlParams.get('duration_min');
            const durationMax = urlParams.get('duration_max');
            if (durationMin && durationMax) {
                if (durationMax === '999999') {
                    durationFilter.value = durationMin + '+';
                } else {
                    durationFilter.value = durationMin + '-' + durationMax;
                }
            }
        }

        // Add event listeners
        if (categoryFilter) {
            categoryFilter.addEventListener('change', applyFilters);
        }
        if (priceFilter) {
            priceFilter.addEventListener('change', applyFilters);
        }
        if (durationFilter) {
            durationFilter.addEventListener('change', applyFilters);
        }
    }

    function applyFilters() {
        const url = new URL(window.location);

        // Category filter
        const categoryFilter = document.getElementById('category-filter');
        if (categoryFilter && categoryFilter.value) {
            url.searchParams.set('category_id', categoryFilter.value);
        } else {
            url.searchParams.delete('category_id');
        }

        // Price filter
        const priceFilter = document.getElementById('price-filter');
        if (priceFilter && priceFilter.value) {
            const priceRange = priceFilter.value;
            if (priceRange.includes('+')) {
                const minPrice = priceRange.replace('+', '');
                url.searchParams.set('price_min', minPrice);
                url.searchParams.set('price_max', '999999');
            } else if (priceRange.includes('-')) {
                const [minPrice, maxPrice] = priceRange.split('-');
                url.searchParams.set('price_min', minPrice);
                url.searchParams.set('price_max', maxPrice);
            }
        } else {
            url.searchParams.delete('price_min');
            url.searchParams.delete('price_max');
        }

        // Duration filter
        const durationFilter = document.getElementById('duration-filter');
        if (durationFilter && durationFilter.value) {
            const durationRange = durationFilter.value;
            if (durationRange.includes('+')) {
                const minDuration = durationRange.replace('+', '');
                url.searchParams.set('duration_min', minDuration);
                url.searchParams.set('duration_max', '999999');
            } else if (durationRange.includes('-')) {
                const [minDuration, maxDuration] = durationRange.split('-');
                url.searchParams.set('duration_min', minDuration);
                url.searchParams.set('duration_max', maxDuration);
            }
        } else {
            url.searchParams.delete('duration_min');
            url.searchParams.delete('duration_max');
        }

        // Reset to first page
        url.searchParams.set('page_num', '1');

        // Navigate to filtered URL
        window.location.href = url.toString();
    }
</script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script>
    // Export services function
    function exportServices(format = 'csv') {
        if (format === 'xlsx') {
            exportServicesXLSX();
        } else {
            exportServicesCSV();
        }
    }

    function exportServicesCSV() {
        const urlParams = new URLSearchParams(window.location.search);
        const params = new URLSearchParams();

        // Add current filters to export
        if (urlParams.get('search')) params.append('search', urlParams.get('search'));
        if (urlParams.get('category_id')) params.append('category_id', urlParams.get('category_id'));
        if (urlParams.get('price_min')) params.append('price_min', urlParams.get('price_min'));
        if (urlParams.get('price_max')) params.append('price_max', urlParams.get('price_max'));

        params.append('action', 'export_services');
        params.append('format', 'csv');

        window.open('/store-admin/controllers/export.php?' + params.toString(), '_blank');
    }

    async function exportServicesXLSX() {
        try {
            // Fetch data from server
            const urlParams = new URLSearchParams(window.location.search);
            const params = new URLSearchParams();

            if (urlParams.get('search')) params.append('search', urlParams.get('search'));
            if (urlParams.get('category_id')) params.append('category_id', urlParams.get('category_id'));
            if (urlParams.get('price_min')) params.append('price_min', urlParams.get('price_min'));
            if (urlParams.get('price_max')) params.append('price_max', urlParams.get('price_max'));

            params.append('action', 'get_services_data');

            const response = await fetch('/store-admin/controllers/ajax.php?' + params.toString());
            const result = await response.json();

            if (!result.success) {
                throw new Error(result.error || 'Failed to fetch data');
            }

            // Create workbook
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(result.data);

            // Add worksheet to workbook
            XLSX.utils.book_append_sheet(wb, ws, 'Services');

            // Download file
            XLSX.writeFile(wb, `services_export_${new Date().toISOString().split('T')[0]}.xlsx`);

        } catch (error) {
            console.error('Export error:', error);
            alert('Export failed: ' + error.message);
        }
    }

    function downloadServicesTemplate() {
        const templateData = [{
                name: 'Haircut',
                description: 'Professional haircut service',
                price: 25.00,
                duration: 30,
                preparation_time: 5,
                cleanup_time: 5,
                category_name: 'Hair Services',
                is_active: 1
            },
            {
                name: 'Hair Coloring',
                description: 'Professional hair coloring service',
                price: 75.00,
                duration: 120,
                preparation_time: 10,
                cleanup_time: 15,
                category_name: 'Hair Services',
                is_active: 1
            }
        ];

        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(templateData);

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, 'Services Template');

        // Download template
        XLSX.writeFile(wb, 'services_import_template.xlsx');
    }

    function importServices() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.xlsx,.xls';
        input.onchange = function(e) {
            const file = e.target.files[0];
            if (file) {
                processServicesImport(file);
            }
        };
        input.click();
    }

    async function processServicesImport(file) {
        try {
            const data = await file.arrayBuffer();
            const workbook = XLSX.read(data);
            const worksheet = workbook.Sheets[workbook.SheetNames[0]];
            const jsonData = XLSX.utils.sheet_to_json(worksheet);

            if (jsonData.length === 0) {
                alert('No data found in the file');
                return;
            }

            // Send data to server
            const formData = new FormData();
            formData.append('action', 'import_services');
            formData.append('data', JSON.stringify(jsonData));

            const response = await fetch('/store-admin/controllers/ajax.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                alert(`Successfully imported ${result.imported} services`);
                window.location.reload();
            } else {
                alert('Import failed: ' + (result.error || 'Unknown error'));
            }

        } catch (error) {
            console.error('Import error:', error);
            alert('Import failed: ' + error.message);
        }
    }
</script>
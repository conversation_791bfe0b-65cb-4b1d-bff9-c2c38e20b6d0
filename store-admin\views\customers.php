<?php

/**
 * Customers View
 * Manage customers
 */

// Get search and pagination parameters
$searchParams = AdminHelpers::getSearchParams();
$search = $searchParams['search'];
$pageNum = $searchParams['page_num'];
$perPage = $searchParams['per_page'];

// Get filter parameters
$statusFilter = $_GET['status'] ?? '';
$visitFilter = $_GET['visit'] ?? '';
$spentFilter = $_GET['spent'] ?? '';

// Build search query - search by name, email, phone, and notes
$searchWhere = AdminHelpers::buildSearchWhere($search, ['name', 'email', 'phone', 'notes']);

// Add filters to WHERE clause
$additionalWhere = [];
$additionalParams = [];

if ($statusFilter) {
    switch ($statusFilter) {
        case 'active':
            $additionalWhere[] = "c.id IN (SELECT DISTINCT customer_id FROM reservations WHERE created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH))";
            break;
        case 'vip':
            $additionalWhere[] = "c.id IN (SELECT customer_id FROM reservations GROUP BY customer_id HAVING COUNT(*) >= 5)";
            break;
        case 'new':
            $additionalWhere[] = "c.created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
            break;
    }
}

if ($visitFilter) {
    switch ($visitFilter) {
        case 'week':
            $additionalWhere[] = "c.id IN (SELECT DISTINCT customer_id FROM reservations WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 WEEK))";
            break;
        case 'month':
            $additionalWhere[] = "c.id IN (SELECT DISTINCT customer_id FROM reservations WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH))";
            break;
        case 'quarter':
            $additionalWhere[] = "c.id IN (SELECT DISTINCT customer_id FROM reservations WHERE created_at >= DATE_SUB(NOW(), INTERVAL 3 MONTH))";
            break;
    }
}

if ($spentFilter) {
    switch ($spentFilter) {
        case '0-50':
            $additionalWhere[] = "c.id IN (SELECT customer_id FROM reservations r JOIN services s ON r.service_id = s.id GROUP BY customer_id HAVING SUM(s.price) BETWEEN 0 AND 50)";
            break;
        case '50-100':
            $additionalWhere[] = "c.id IN (SELECT customer_id FROM reservations r JOIN services s ON r.service_id = s.id GROUP BY customer_id HAVING SUM(s.price) BETWEEN 50 AND 100)";
            break;
        case '100+':
            $additionalWhere[] = "c.id IN (SELECT customer_id FROM reservations r JOIN services s ON r.service_id = s.id GROUP BY customer_id HAVING SUM(s.price) > 100)";
            break;
    }
}

// Combine search and filter conditions
$whereClause = $searchWhere['where'];
$allParams = $searchWhere['params'];

if (!empty($additionalWhere)) {
    $additionalWhereStr = implode(' AND ', $additionalWhere);
    if ($whereClause) {
        $whereClause .= " AND " . $additionalWhereStr;
    } else {
        $whereClause = "WHERE " . $additionalWhereStr;
    }
    $allParams = array_merge($allParams, $additionalParams);
}

// Get total count for pagination first
$countSql = "SELECT COUNT(*) as total FROM customers c {$whereClause}";
$totalCount = $db->fetchRow($countSql, $allParams)['total'];

// Create pagination
$pagination = new Pagination($totalCount, $perPage, $pageNum);

// Get customers with reservation count using proper SQL pagination (simplified to avoid schema issues)
$sql = "SELECT c.*, COUNT(r.id) as reservation_count,
               MAX(r.created_at) as last_reservation_date
        FROM customers c
        LEFT JOIN reservations r ON c.id = r.customer_id
        {$whereClause}
        GROUP BY c.id
        ORDER BY c.name ASC
        LIMIT {$perPage} OFFSET {$pagination->getOffset()}";

try {
    $paginatedCustomers = $db->fetchAll($sql, $allParams);
} catch (Exception $e) {
    // If reservations table has schema issues, just get customers without reservation data
    $sql = "SELECT c.*, 0 as reservation_count, NULL as last_reservation_date
            FROM customers c
            {$whereClause}
            ORDER BY c.name ASC
            LIMIT {$perPage} OFFSET {$pagination->getOffset()}";
    $paginatedCustomers = $db->fetchAll($sql, $allParams);
}
?>

<!-- Main Toolbar -->
<div class="main-toolbar">
    <div class="toolbar-left">
        <a href="/store-admin/?page=add-customer" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add Customer
        </a>
        <div class="view-toggle">
            <button class="active" data-view="cards">
                <i class="fas fa-th-large"></i> Cards
            </button>
            <button data-view="table">
                <i class="fas fa-list"></i> Table
            </button>
        </div>
    </div>
    <div class="toolbar-right">
        <div class="dropdown">
            <button class="btn btn-primary dropdown-toggle" type="button" data-toggle="dropdown">
                <i class="fas fa-download"></i> Export
            </button>
            <div class="dropdown-menu">
                <a class="dropdown-item" href="#" onclick="exportCustomers('csv')">
                    <i class="fas fa-file-csv"></i> Export as CSV
                </a>
                <a class="dropdown-item" href="#" onclick="exportCustomers('xlsx')">
                    <i class="fas fa-file-excel"></i> Export as XLSX
                </a>
            </div>
        </div>
        <div class="dropdown">
            <button class="btn btn-secondary dropdown-toggle" type="button" data-toggle="dropdown">
                <i class="fas fa-upload"></i> Import
            </button>
            <div class="dropdown-menu">
                <a class="dropdown-item" href="#" onclick="downloadCustomersTemplate()">
                    <i class="fas fa-download"></i> Download Template
                </a>
                <a class="dropdown-item" href="#" onclick="importCustomers()">
                    <i class="fas fa-upload"></i> Import XLSX
                </a>
            </div>
        </div>
        <button class="btn btn-secondary" onclick="toggleFilters()" id="filter-toggle-btn">
            <i class="fas fa-filter"></i> Filters
        </button>
        <div class="search-bar">
            <input type="text" placeholder="Search by name, email, phone..." value="<?php echo htmlspecialchars($search); ?>" id="customer-search">
            <button type="button" class="search-btn" onclick="performSearch(document.getElementById('customer-search').value)">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>
</div>

<!-- Filters Section -->
<div class="filters-section" id="filters-section" style="display: none;">
    <div class="filters-header">
        <h3><i class="fas fa-filter"></i> Filters</h3>
        <button class="btn btn-outline btn-sm" onclick="clearFilters()">
            <i class="fas fa-times"></i> Clear All
        </button>
    </div>
    <div class="filters-content">
        <div class="filter-group">
            <label class="filter-label">Status:</label>
            <select class="form-control" id="status-filter" onchange="applyFilter('status', this.value)">
                <option value="">All Customers</option>
                <option value="active" <?= $statusFilter === 'active' ? 'selected' : '' ?>>Active</option>
                <option value="vip" <?= $statusFilter === 'vip' ? 'selected' : '' ?>>VIP</option>
                <option value="new" <?= $statusFilter === 'new' ? 'selected' : '' ?>>New</option>
            </select>
        </div>
        <div class="filter-group">
            <label class="filter-label">Last Visit:</label>
            <select class="form-control" id="visit-filter" onchange="applyFilter('visit', this.value)">
                <option value="">All Time</option>
                <option value="week" <?= $visitFilter === 'week' ? 'selected' : '' ?>>This Week</option>
                <option value="month" <?= $visitFilter === 'month' ? 'selected' : '' ?>>This Month</option>
                <option value="quarter" <?= $visitFilter === 'quarter' ? 'selected' : '' ?>>This Quarter</option>
            </select>
        </div>
        <div class="filter-group">
            <label class="filter-label">Total Spent:</label>
            <select class="form-control" id="spent-filter" onchange="applyFilter('spent', this.value)">
                <option value="">Any Amount</option>
                <option value="0-50" <?= $spentFilter === '0-50' ? 'selected' : '' ?>>€0 - €50</option>
                <option value="50-100" <?= $spentFilter === '50-100' ? 'selected' : '' ?>>€50 - €100</option>
                <option value="100+" <?= $spentFilter === '100+' ? 'selected' : '' ?>>€100+</option>
            </select>
        </div>
    </div>
</div>



<!-- Enhanced Customers Grid -->
<div class="entity-grid" id="customers-grid">
    <?php if (empty($paginatedCustomers)): ?>
        <div class="empty-state">
            <i class="fas fa-users fa-2x text-muted"></i>
            <h3>No customers found</h3>
            <p>Start by adding your first customer or adjust your search criteria.</p>
            <button class="btn btn-primary btn-lg" onclick="addCustomer()">
                <i class="fas fa-plus"></i> Add First Customer
            </button>
        </div>
    <?php else: ?>
        <?php
        // Include the entity card component
        require_once __DIR__ . '/components/entity-card.php';

        foreach ($paginatedCustomers as $customer):
            // Prepare customer data for the card component
            $reservationCount = $customer['reservation_count'] ?? 0;
            $lastVisit = $customer['last_reservation_date'] ?? null;

            // Determine customer status
            if ($reservationCount > 10) {
                $customerStatus = 'vip';
            } elseif ($reservationCount > 3) {
                $customerStatus = 'regular';
            } else {
                $customerStatus = 'new';
            }

            // Prepare customer data for card
            $customerData = [
                'id' => $customer['id'],
                'name' => $customer['name'],
                'email' => $customer['email'] ?? '',
                'phone' => $customer['phone'] ?? '',
                'language' => $customer['language'] ?? 'EL',
                'status' => $customerStatus === 'vip' ? 'active' : ($customerStatus === 'regular' ? 'active' : 'inactive'),
                'total_visits' => $reservationCount,
                'total_spent' => $reservationCount * 35, // Estimated value
                'last_visit' => $lastVisit,
                'notes' => $customer['notes'] ?? ''
            ];

            // Render the customer card with actions enabled
            echo renderEntityCard($customerData, 'customer', [
                'show_checkbox' => false,
                'show_actions' => true
            ]);
        endforeach; ?>

        <!-- Add Customer Card -->
        <div class="entity-card add-card" onclick="addCustomer()">
            <div class="entity-card-body" style="display: flex; align-items: center; justify-content: center; min-height: 200px; flex-direction: column; cursor: pointer;">
                <i class="fas fa-plus fa-2x" style="margin-bottom: 16px; color: var(--gray-400);"></i>
                <h3 style="color: var(--gray-600); margin: 0; font-size: var(--text-lg);">Add New Customer</h3>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Pagination -->
<?php if ($pagination->getTotalPages() > 1): ?>
    <div class="pagination-container">
        <?php echo $pagination->render(); ?>
    </div>
<?php endif; ?>

<!-- Send Email Modal -->
<div id="sendEmailModal" class="modal">
    <div class="modal-content modal-large">
        <div class="modal-header">
            <h3><i class="fas fa-envelope"></i> <?= at('send_email', 'Send Email') ?></h3>
            <button class="modal-close" onclick="closeSendEmailModal()">&times;</button>
        </div>
        <div class="modal-body">
            <form id="sendEmailForm">
                <input type="hidden" id="emailCustomerId" name="customer_id">

                <div class="form-group">
                    <label for="emailSubject"><?= at('subject', 'Subject') ?> *</label>
                    <input type="text" id="emailSubject" name="subject" required
                        placeholder="<?= at('email_subject_placeholder', 'Enter email subject...') ?>">
                </div>

                <div class="form-group">
                    <label for="emailContent"><?= at('email_content', 'Email Content') ?> *</label>
                    <textarea id="emailContent" name="content" rows="10" required
                        placeholder="<?= at('email_content_placeholder', 'Enter email content...') ?>"></textarea>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeSendEmailModal()">
                        <?= at('cancel', 'Cancel') ?>
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i> <?= at('send_email', 'Send Email') ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    function sendEmailToCustomer(customerId) {
        document.getElementById('emailCustomerId').value = customerId;
        document.getElementById('sendEmailModal').style.display = 'block';
    }

    function closeSendEmailModal() {
        document.getElementById('sendEmailModal').style.display = 'none';
        document.getElementById('sendEmailForm').reset();
    }

    // Handle form submission
    document.getElementById('sendEmailForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const data = Object.fromEntries(formData);

        showLoadingOverlay('Sending email...');

        fetch('/store-admin/controllers/ajax.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'send_custom_email',
                    ...data
                })
            })
            .then(response => response.json())
            .then(data => {
                hideLoadingOverlay();
                if (data.success) {
                    showNotification('Email sent successfully!', 'success');
                    closeSendEmailModal();
                } else {
                    showNotification('Failed to send email: ' + (data.error || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                hideLoadingOverlay();
                showNotification('Error sending email: ' + error.message, 'error');
            });
    });
</script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script>
    // Export customers function
    function exportCustomers(format = 'csv') {
        if (format === 'xlsx') {
            exportCustomersXLSX();
        } else {
            exportCustomersCSV();
        }
    }

    function exportCustomersCSV() {
        const urlParams = new URLSearchParams(window.location.search);
        const params = new URLSearchParams();

        // Add current filters to export
        if (urlParams.get('search')) params.append('search', urlParams.get('search'));
        if (urlParams.get('status')) params.append('status', urlParams.get('status'));
        if (urlParams.get('date_from')) params.append('date_from', urlParams.get('date_from'));
        if (urlParams.get('date_to')) params.append('date_to', urlParams.get('date_to'));

        params.append('action', 'export_customers');
        params.append('format', 'csv');

        window.open('/store-admin/controllers/export.php?' + params.toString(), '_blank');
    }

    async function exportCustomersXLSX() {
        try {
            // Fetch data from server
            const urlParams = new URLSearchParams(window.location.search);
            const params = new URLSearchParams();

            if (urlParams.get('search')) params.append('search', urlParams.get('search'));
            if (urlParams.get('status')) params.append('status', urlParams.get('status'));
            if (urlParams.get('date_from')) params.append('date_from', urlParams.get('date_from'));
            if (urlParams.get('date_to')) params.append('date_to', urlParams.get('date_to'));

            params.append('action', 'get_customers_data');

            const response = await fetch('/store-admin/controllers/ajax.php?' + params.toString());
            const result = await response.json();

            if (!result.success) {
                throw new Error(result.error || 'Failed to fetch data');
            }

            // Create workbook
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(result.data);

            // Add worksheet to workbook
            XLSX.utils.book_append_sheet(wb, ws, 'Customers');

            // Download file
            XLSX.writeFile(wb, `customers_export_${new Date().toISOString().split('T')[0]}.xlsx`);

        } catch (error) {
            console.error('Export error:', error);
            alert('Export failed: ' + error.message);
        }
    }

    function downloadCustomersTemplate() {
        const templateData = [{
                name: 'John Doe',
                email: '<EMAIL>',
                phone: '+1234567890',
                language: 'en',
                notes: 'VIP customer'
            },
            {
                name: 'Jane Smith',
                email: '<EMAIL>',
                phone: '+0987654321',
                language: 'el',
                notes: 'Regular customer'
            }
        ];

        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(templateData);

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, 'Customers Template');

        // Download template
        XLSX.writeFile(wb, 'customers_import_template.xlsx');
    }

    function importCustomers() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.xlsx,.xls';
        input.onchange = function(e) {
            const file = e.target.files[0];
            if (file) {
                processCustomersImport(file);
            }
        };
        input.click();
    }

    async function processCustomersImport(file) {
        try {
            const data = await file.arrayBuffer();
            const workbook = XLSX.read(data);
            const worksheet = workbook.Sheets[workbook.SheetNames[0]];
            const jsonData = XLSX.utils.sheet_to_json(worksheet);

            if (jsonData.length === 0) {
                alert('No data found in the file');
                return;
            }

            // Send data to server
            const formData = new FormData();
            formData.append('action', 'import_customers');
            formData.append('data', JSON.stringify(jsonData));

            const response = await fetch('/store-admin/controllers/ajax.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                alert(`Successfully imported ${result.imported} customers`);
                window.location.reload();
            } else {
                alert('Import failed: ' + (result.error || 'Unknown error'));
            }

        } catch (error) {
            console.error('Import error:', error);
            alert('Import failed: ' + error.message);
        }
    }

    // Close modal when clicking outside
    window.onclick = function(event) {
        const modal = document.getElementById('sendEmailModal');
        if (event.target === modal) {
            closeSendEmailModal();
        }
    }
</script>
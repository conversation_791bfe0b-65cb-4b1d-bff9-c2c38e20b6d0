<?php
// Start session for authentication
session_start();

// Initialize Application if not already done
if (!class_exists('Application')) {
    require_once __DIR__ . '/../core/Application.php';
    Application::init();
}

// Check authentication first
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(401);
    echo json_encode(['error' => 'Authentication required']);
    exit;
}

// Ensure we're using the correct tenant context
$currentTenant = TenantManager::getCurrentTenant();
if (!$currentTenant) {
    // If no tenant detected, check if there's a tenant in the session or default to 'demo'
    $defaultTenant = $_SESSION['tenant'] ?? 'demo';
    $_GET['tenant'] = $defaultTenant;

    // Re-initialize TenantManager with the forced tenant
    require_once __DIR__ . '/../../shared/tenant_manager.php';
    TenantManager::init();

    $currentTenant = TenantManager::getCurrentTenant();
}

// Get database with error handling
try {
    $db = Application::getDb();
    if (!$db) {
        throw new Exception('Database connection failed');
    }
} catch (Exception $e) {
    error_log("Export error - Database connection: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Database connection failed']);
    exit;
}

// Get parameters
$action = $_GET['action'] ?? '';
$format = $_GET['format'] ?? 'csv';

// Handle different export actions with error handling
try {
    switch ($action) {
        case 'export_customers':
            exportCustomers($db, $_GET);
            break;
        case 'export_employees':
            exportEmployees($db, $_GET);
            break;
        case 'export_services':
            exportServices($db, $_GET);
            break;
        case 'export_reservations':
            exportReservations($db, $_GET);
            break;
        case 'export_email_logs':
            exportEmailLogs($db, $_GET);
            break;
        case 'export_categories':
            exportCategories($db, $_GET);
            break;
        case 'export_translations':
            exportTranslations($db, $_GET);
            break;
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid export action']);
            exit;
    }
} catch (Exception $e) {
    error_log("Export error - Action '$action': " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Export failed: ' . $e->getMessage()]);
    exit;
}

function exportCustomers($db, $params)
{
    $search = $params['search'] ?? '';
    $status = $params['status'] ?? '';
    $dateFrom = $params['date_from'] ?? '';
    $dateTo = $params['date_to'] ?? '';

    $sql = "SELECT id, name, email, phone, language, notes, created_at, updated_at FROM customers WHERE 1=1";
    $sqlParams = [];

    if ($search) {
        $sql .= " AND (name LIKE ? OR email LIKE ? OR phone LIKE ?)";
        $searchTerm = "%$search%";
        $sqlParams[] = $searchTerm;
        $sqlParams[] = $searchTerm;
        $sqlParams[] = $searchTerm;
    }

    if ($status) {
        $sql .= " AND language = ?";
        $sqlParams[] = $status;
    }

    if ($dateFrom) {
        $sql .= " AND created_at >= ?";
        $sqlParams[] = $dateFrom;
    }

    if ($dateTo) {
        $sql .= " AND created_at <= ?";
        $sqlParams[] = $dateTo . ' 23:59:59';
    }

    $sql .= " ORDER BY created_at DESC";

    $customers = $db->fetchAll($sql, $sqlParams);
    outputCSV($customers, 'customers_export_' . date('Y-m-d'));
}

function exportEmployees($db, $params)
{
    $search = $params['search'] ?? '';
    $status = $params['status'] ?? '';
    $position = $params['position'] ?? '';

    $sql = "SELECT id, name, email, phone, position, is_active, created_at, updated_at FROM employees WHERE 1=1";
    $sqlParams = [];

    if ($search) {
        $sql .= " AND (name LIKE ? OR email LIKE ? OR phone LIKE ? OR position LIKE ?)";
        $searchTerm = "%$search%";
        $sqlParams[] = $searchTerm;
        $sqlParams[] = $searchTerm;
        $sqlParams[] = $searchTerm;
        $sqlParams[] = $searchTerm;
    }

    if ($status) {
        $sql .= " AND is_active = ?";
        $sqlParams[] = $status;
    }

    if ($position) {
        $sql .= " AND position = ?";
        $sqlParams[] = $position;
    }

    $sql .= " ORDER BY created_at DESC";

    $employees = $db->fetchAll($sql, $sqlParams);
    outputCSV($employees, 'employees_export_' . date('Y-m-d'));
}

function exportServices($db, $params)
{
    try {
        $search = $params['search'] ?? '';
        $categoryId = $params['category_id'] ?? '';
        $priceMin = $params['price_min'] ?? '';
        $priceMax = $params['price_max'] ?? '';

        $sql = "SELECT s.id, s.name, s.description, s.price, s.duration, s.is_active, c.name as category_name
                FROM services s
                LEFT JOIN categories c ON s.category_id = c.id
                WHERE 1=1";
        $sqlParams = [];

        if ($search) {
            $sql .= " AND (s.name LIKE ? OR s.description LIKE ? OR c.name LIKE ?)";
            $searchTerm = "%$search%";
            $sqlParams[] = $searchTerm;
            $sqlParams[] = $searchTerm;
            $sqlParams[] = $searchTerm;
        }

        if ($categoryId) {
            $sql .= " AND s.category_id = ?";
            $sqlParams[] = $categoryId;
        }

        if ($priceMin) {
            $sql .= " AND s.price >= ?";
            $sqlParams[] = $priceMin;
        }

        if ($priceMax) {
            $sql .= " AND s.price <= ?";
            $sqlParams[] = $priceMax;
        }

        $sql .= " ORDER BY s.name";

        error_log("Export Services SQL: " . $sql);
        error_log("Export Services Params: " . json_encode($sqlParams));

        $services = $db->fetchAll($sql, $sqlParams);

        if ($services === false) {
            throw new Exception('Failed to fetch services from database');
        }

        outputCSV($services, 'services_export_' . date('Y-m-d'));
    } catch (Exception $e) {
        error_log("exportServices error: " . $e->getMessage());
        throw $e;
    }
}

function exportReservations($db, $params)
{
    $search = $params['search'] ?? '';
    $status = $params['status'] ?? '';
    $dateFrom = $params['date_from'] ?? '';
    $dateTo = $params['date_to'] ?? '';
    $serviceId = $params['service_id'] ?? '';
    $employeeId = $params['employee_id'] ?? '';

    $sql = "SELECT r.id, c.name as customer_name, c.email as customer_email,
                   s.name as service_name, e.name as employee_name,
                   r.date, r.start_time, r.end_time, r.status, r.price, r.notes
            FROM reservations r
            LEFT JOIN customers c ON r.customer_id = c.id
            LEFT JOIN services s ON r.service_id = s.id
            LEFT JOIN employees e ON r.employee_id = e.id
            WHERE 1=1";
    $sqlParams = [];

    if ($search) {
        $sql .= " AND (c.name LIKE ? OR c.email LIKE ? OR s.name LIKE ? OR e.name LIKE ?)";
        $searchTerm = "%$search%";
        $sqlParams[] = $searchTerm;
        $sqlParams[] = $searchTerm;
        $sqlParams[] = $searchTerm;
        $sqlParams[] = $searchTerm;
    }

    if ($status) {
        $sql .= " AND r.status = ?";
        $sqlParams[] = $status;
    }

    if ($dateFrom) {
        $sql .= " AND r.date >= ?";
        $sqlParams[] = $dateFrom;
    }

    if ($dateTo) {
        $sql .= " AND r.date <= ?";
        $sqlParams[] = $dateTo;
    }

    if ($serviceId) {
        $sql .= " AND r.service_id = ?";
        $sqlParams[] = $serviceId;
    }

    if ($employeeId) {
        $sql .= " AND r.employee_id = ?";
        $sqlParams[] = $employeeId;
    }

    $sql .= " ORDER BY r.date DESC, r.start_time DESC";

    $reservations = $db->fetchAll($sql, $sqlParams);
    outputCSV($reservations, 'reservations_export_' . date('Y-m-d'));
}

function exportEmailLogs($db, $params)
{
    $search = $params['search'] ?? '';
    $customerId = $params['customer_id'] ?? '';
    $dateFrom = $params['date_from'] ?? '';
    $dateTo = $params['date_to'] ?? '';

    $sql = "SELECT el.id, c.name as customer_name, c.email as customer_email,
                   el.subject, el.email_type, el.sent_at, el.status
            FROM email_logs el
            LEFT JOIN customers c ON el.customer_id = c.id
            WHERE 1=1";
    $sqlParams = [];

    if ($search) {
        $sql .= " AND (c.name LIKE ? OR c.email LIKE ? OR el.subject LIKE ?)";
        $searchTerm = "%$search%";
        $sqlParams[] = $searchTerm;
        $sqlParams[] = $searchTerm;
        $sqlParams[] = $searchTerm;
    }

    if ($customerId) {
        $sql .= " AND el.customer_id = ?";
        $sqlParams[] = $customerId;
    }

    if ($dateFrom) {
        $sql .= " AND el.sent_at >= ?";
        $sqlParams[] = $dateFrom;
    }

    if ($dateTo) {
        $sql .= " AND el.sent_at <= ?";
        $sqlParams[] = $dateTo . ' 23:59:59';
    }

    $sql .= " ORDER BY el.sent_at DESC";

    $emailLogs = $db->fetchAll($sql, $sqlParams);
    outputCSV($emailLogs, 'email_logs_export_' . date('Y-m-d'));
}

function exportCategories($db, $params)
{
    $search = $params['search'] ?? '';
    $sort = $params['sort'] ?? '';
    $status = $params['status'] ?? '';

    $sql = "SELECT c.id, c.name, c.description, c.icon, c.color, c.is_active, c.sort_order,
                   c.created_at, c.updated_at, COUNT(s.id) as services_count
            FROM categories c
            LEFT JOIN services s ON c.id = s.category_id AND s.is_active = 1
            WHERE 1=1";
    $sqlParams = [];

    if ($search) {
        $sql .= " AND (c.name LIKE ? OR c.description LIKE ?)";
        $searchTerm = "%$search%";
        $sqlParams[] = $searchTerm;
        $sqlParams[] = $searchTerm;
    }

    if ($status) {
        if ($status === 'active') {
            $sql .= " AND c.is_active = 1";
        } elseif ($status === 'inactive') {
            $sql .= " AND c.is_active = 0";
        }
    }

    $sql .= " GROUP BY c.id";

    // Add sorting
    switch ($sort) {
        case 'name':
            $sql .= " ORDER BY c.name ASC";
            break;
        case 'services_count':
            $sql .= " ORDER BY services_count DESC";
            break;
        case 'created_at':
            $sql .= " ORDER BY c.created_at DESC";
            break;
        default:
            $sql .= " ORDER BY c.sort_order ASC, c.name ASC";
    }

    $categories = $db->fetchAll($sql, $sqlParams);
    outputCSV($categories, 'categories_export_' . date('Y-m-d'));
}

function exportTranslations($db, $params)
{
    $search = $params['search'] ?? '';
    $category = $params['category'] ?? '';

    $sql = "SELECT key, category, value_el, value_en, created_at, updated_at
            FROM translations
            WHERE 1=1";
    $sqlParams = [];

    // Only export client-facing translations
    $clientCategories = ['general', 'booking_system', 'booking_flow', 'user_interface', 'services', 'categories', 'client', 'ui', 'dates'];
    $sql .= " AND category IN ('" . implode("','", $clientCategories) . "')";

    if ($search) {
        $sql .= " AND (key LIKE ? OR value_el LIKE ? OR value_en LIKE ? OR category LIKE ?)";
        $searchTerm = "%$search%";
        $sqlParams[] = $searchTerm;
        $sqlParams[] = $searchTerm;
        $sqlParams[] = $searchTerm;
        $sqlParams[] = $searchTerm;
    }

    if ($category && $category !== 'all' && in_array($category, $clientCategories)) {
        $sql .= " AND category = ?";
        $sqlParams[] = $category;
    }

    $sql .= " ORDER BY category ASC, key ASC";

    $translations = $db->fetchAll($sql, $sqlParams);
    outputCSV($translations, 'translations_export_' . date('Y-m-d'));
}

function outputCSV($data, $filename)
{
    try {
        // Validate filename
        $filename = preg_replace('/[^a-zA-Z0-9_\-]/', '_', $filename);

        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');

        $output = fopen('php://output', 'w');

        if ($output === false) {
            throw new Exception('Failed to open output stream');
        }

        if (!empty($data)) {
            // Write BOM for UTF-8
            fwrite($output, "\xEF\xBB\xBF");

            // Write headers
            if (!fputcsv($output, array_keys($data[0]))) {
                throw new Exception('Failed to write CSV headers');
            }

            // Write data
            foreach ($data as $row) {
                if (!fputcsv($output, $row)) {
                    throw new Exception('Failed to write CSV row');
                }
            }
        } else {
            // Write BOM and empty message
            fwrite($output, "\xEF\xBB\xBF");
            fputcsv($output, ['message']);
            fputcsv($output, ['No data available for export']);
        }

        fclose($output);
        exit;
    } catch (Exception $e) {
        error_log("outputCSV error: " . $e->getMessage());
        throw $e;
    }
}

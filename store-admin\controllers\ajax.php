<?php

/**
 * AJAX Controller
 * Handles all AJAX requests for the store admin
 */

// Start session for CSRF token validation
session_start();

// Clear any previous output that might have been generated
while (ob_get_level()) {
    ob_end_clean();
}

header('Content-Type: application/json');

// Initialize Application if not already done
if (!class_exists('Application')) {
    require_once __DIR__ . '/../core/Application.php';
    Application::init();
}

// Ensure we're using the correct tenant context
// For store-admin, we need to ensure a valid tenant is always set
$currentTenant = TenantManager::getCurrentTenant();
if (!$currentTenant) {
    // If no tenant detected, check if there's a tenant in the session or default to 'demo'
    $defaultTenant = $_SESSION['tenant'] ?? 'demo';
    $_GET['tenant'] = $defaultTenant;

    // Re-initialize TenantManager with the forced tenant
    require_once __DIR__ . '/../../shared/tenant_manager.php';
    TenantManager::init();

    $currentTenant = TenantManager::getCurrentTenant();
}

// Include AdminHelpers class
if (!class_exists('AdminHelpers')) {
    require_once __DIR__ . '/../core/AdminHelpers.php';
}

try {
    $action = $_POST['action'] ?? $_GET['action'] ?? '';
    $db = Application::getDb();



    switch ($action) {
        case 'get_services':
            echo json_encode(getServices($_POST, $db));
            break;

        case 'get_available_slots':
            echo json_encode(getAvailableSlots($_POST, $db));
            break;
        case 'get_categories_data':
            echo json_encode(getCategoriesData($db, $_GET));
            break;
        case 'get_translations_data':
            echo json_encode(getTranslationsData($db, $_GET));
            break;
        case 'import_categories':
            echo json_encode(importCategories($db, $_POST));
            break;
        case 'import_translations':
            echo json_encode(importTranslations($db, $_POST));
            break;
        case 'get_customers_data':
            echo json_encode(getCustomersData($db, $_GET));
            break;
        case 'import_customers':
            echo json_encode(importCustomers($db, $_POST));
            break;
        case 'get_services_data':
            echo json_encode(getServicesData($db, $_GET));
            break;
        case 'import_services':
            echo json_encode(importServices($db, $_POST));
            break;
        case 'get_employees_data':
            echo json_encode(getEmployeesData($db, $_GET));
            break;
        case 'import_employees':
            echo json_encode(importEmployees($db, $_POST));
            break;
        case 'get_reservations_data':
            echo json_encode(getReservationsData($db, $_GET));
            break;
        case 'import_reservations':
            echo json_encode(importReservations($db, $_POST));
            break;

        case 'check_date_availability':
            echo json_encode(checkDateAvailability($_POST, $db));
            break;

        case 'delete_item':
            echo json_encode(deleteItem($_POST, $db));
            break;

        case 'delete_customer':
            echo json_encode(deleteSpecificEntity($_POST, $db, 'customers'));
            break;

        case 'delete_service':
            echo json_encode(deleteSpecificEntity($_POST, $db, 'services'));
            break;

        case 'delete_employee':
            echo json_encode(deleteSpecificEntity($_POST, $db, 'employees'));
            break;

        case 'delete_category':
            echo json_encode(deleteSpecificEntity($_POST, $db, 'categories'));
            break;

        case 'delete_reservation':
            echo json_encode(deleteSpecificEntity($_POST, $db, 'reservations'));
            break;

        case 'assign_service_to_category':
            echo json_encode(assignServiceToCategory($_POST, $db));
            break;

        case 'unassign_service_from_category':
            echo json_encode(unassignServiceFromCategory($_POST, $db));
            break;

        case 'get_form':
            echo json_encode(getForm($_POST, $db));
            break;

        case 'get_dashboard_stats':
            echo json_encode(getDashboardStats($db));
            break;

        case 'get_category_data':
            echo json_encode(getCategoryData($_POST, $db));
            break;

        case 'get_service_data':
            echo json_encode(getServiceData($_POST, $db));
            break;

        case 'save_translations':
            echo json_encode(saveTranslations($_POST, $db));
            break;

        case 'get_employee_data':
            echo json_encode(getEmployeeData($_POST, $db));
            break;

        case 'get_customer_data':
            echo json_encode(getCustomerData($_POST, $db));
            break;

        case 'get_reservation_data':
            echo json_encode(getReservationData($_POST, $db));
            break;

        case 'save_category':
            echo json_encode(saveCategory($_POST, $db));
            break;

        case 'save_service':
            echo json_encode(saveService($_POST, $db));
            break;

        case 'save_employee':
            echo json_encode(saveEmployee($_POST, $db));
            break;

        case 'save_customer':
            echo json_encode(saveCustomer($_POST, $db));
            break;

        case 'save_reservation':
            echo json_encode(saveReservation($_POST, $db));
            break;

        case 'update_reservation_status':
            echo json_encode(updateReservationStatus($_POST, $db));
            break;

        case 'bulk_update_reservation_status':
            echo json_encode(bulkUpdateReservationStatus($_POST, $db));
            break;

        case 'get_day_reservations':
            echo json_encode(getDayReservations($_POST, $db));
            break;

        case 'duplicate_service':
            echo json_encode(duplicateService($_POST, $db));
            break;

        case 'bulk_delete':
            echo json_encode(bulkDelete($_POST, $db));
            break;

        case 'bulk_email':
            echo json_encode(bulkEmail($_POST, $db));
            break;

        case 'bulk_sms':
            echo json_encode(bulkSMS($_POST, $db));
            break;

        case 'bulk_export':
            echo json_encode(bulkExport($_POST, $db));
            break;

        case 'bulk_update_prices':
            echo json_encode(bulkUpdatePrices($_POST, $db));
            break;

        case 'bulk_duplicate':
            echo json_encode(bulkDuplicate($_POST, $db));
            break;

        case 'bulk_toggle_status':
            echo json_encode(bulkToggleStatus($_POST, $db));
            break;

        case 'bulk_update_colors':
            echo json_encode(bulkUpdateColors($_POST, $db));
            break;

        case 'bulk_reorder':
            echo json_encode(bulkReorder($_POST, $db));
            break;

        case 'bulk_schedule':
            echo json_encode(bulkSchedule($_POST, $db));
            break;

        case 'bulk_notify':
            echo json_encode(bulkNotify($_POST, $db));
            break;

        case 'bulk_remind':
            echo json_encode(bulkRemind($_POST, $db));
            break;

        case 'bulk_reschedule':
            echo json_encode(bulkReschedule($_POST, $db));
            break;

        case 'debug_bulk_tables':
            echo json_encode(debugBulkTables($db));
            break;

        case 'get_email_logs':
            echo json_encode(getEmailLogs($_POST, $db));
            break;

        case 'get_sms_logs':
            echo json_encode(getSMSLogs($_POST, $db));
            break;

        case 'get_email_log':
            echo json_encode(getEmailLog($_POST, $db));
            break;

        case 'send_custom_email':
            echo json_encode(sendCustomEmail($_POST, $db));
            break;

        default:
            echo json_encode(['success' => false, 'error' => 'Unknown action: ' . $action]);
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
exit;

/**
 * Get services by category
 */
function getServices(array $data, Database $db): array
{
    $categoryId = $data['category_id'] ?? '';
    $sql = "SELECT * FROM services WHERE is_active = 1";
    $params = [];

    if ($categoryId) {
        $sql .= " AND category_id = :category_id";
        $params[':category_id'] = $categoryId;
    }

    $services = $db->fetchAll($sql . " ORDER BY name", $params);
    return ['success' => true, 'services' => $services];
}

/**
 * Get available time slots
 */
function getAvailableSlots(array $data, Database $db): array
{
    $date = $data['date'] ?? '';
    $serviceId = $data['service_id'] ?? '';
    $employeeId = $data['employee_id'] ?? '';

    if (!$date || !$serviceId) {
        return ['success' => false, 'error' => 'Missing required data'];
    }

    $service = $db->fetchRow("SELECT duration FROM services WHERE id = :id", [':id' => $serviceId]);
    if (!$service) {
        return ['success' => false, 'error' => 'Service not found'];
    }

    require_once __DIR__ . '/../../shared/availability_checker.php';
    $availabilityChecker = new AvailabilityChecker($db);
    $slots = $availabilityChecker->getAvailableSlots($serviceId, $date, $employeeId ?: null);

    // Convert simple time strings to the format expected by the frontend
    $formattedSlots = [];
    foreach ($slots as $timeStr) {
        $formattedSlots[] = [
            'time' => $timeStr,
            'available' => true,
            'reason' => 'available'
        ];
    }

    return ['success' => true, 'slots' => $formattedSlots];
}

/**
 * Check date availability
 */
function checkDateAvailability(array $data, Database $db): array
{
    $serviceId = $data['service_id'] ?? '';
    $startDate = $data['start_date'] ?? date('Y-m-d');
    $days = intval($data['days'] ?? 30);

    if (!$serviceId) {
        return ['success' => false, 'error' => 'Missing service ID'];
    }

    $service = $db->fetchRow("SELECT duration FROM services WHERE id = :id", [':id' => $serviceId]);
    if (!$service) {
        return ['success' => false, 'error' => 'Service not found'];
    }

    // Get all employees who can perform this service
    $employees = $db->fetchAll(
        "SELECT e.id FROM employees e
         JOIN employee_services es ON e.id = es.employee_id
         WHERE es.service_id = :service_id AND e.is_active = 1",
        [':service_id' => $serviceId]
    );

    $availability = [];
    $today = new DateTime($startDate);

    for ($i = 0; $i < $days; $i++) {
        $date = clone $today;
        $date->add(new DateInterval("P{$i}D"));
        $dateStr = $date->format('Y-m-d');

        // Check if any employee has available slots for this service on this date
        $hasAvailability = false;
        require_once __DIR__ . '/../../shared/availability_checker.php';
        $availabilityChecker = new AvailabilityChecker($db);

        foreach ($employees as $employee) {
            $slots = $availabilityChecker->getAvailableSlots($serviceId, $dateStr, $employee['id']);
            if (!empty($slots)) {
                $hasAvailability = true;
                break;
            }
        }

        $availability[$dateStr] = $hasAvailability;
    }

    return ['success' => true, 'availability' => $availability];
}

/**
 * Delete item
 */
function deleteItem(array $data, Database $db): array
{
    $table = $data['table'] ?? '';
    $id = $data['id'] ?? '';

    if (!$table || !$id) {
        return ['success' => false, 'error' => 'Missing data'];
    }

    // Validate table name for security
    $allowedTables = ['services', 'categories', 'employees', 'customers', 'reservations'];
    if (!in_array($table, $allowedTables)) {
        return ['success' => false, 'error' => 'Invalid table'];
    }

    // Check dependencies using AdminHelpers
    require_once __DIR__ . '/../core/AdminHelpers.php';
    $dependencyCheck = AdminHelpers::checkDeleteDependencies($db, $table, $id);

    if (!$dependencyCheck['can_delete']) {
        return ['success' => false, 'error' => $dependencyCheck['message']];
    }

    // Handle cascading deletes based on table
    try {
        $db->beginTransaction();

        switch ($table) {
            case 'categories':
                $result = $db->query("DELETE FROM categories WHERE id = :id", [':id' => $id]);
                break;

            case 'services':
                // Delete employee-service assignments first
                $db->query("DELETE FROM employee_services WHERE service_id = :id", [':id' => $id]);
                $result = $db->query("DELETE FROM services WHERE id = :id", [':id' => $id]);
                break;

            case 'employees':
                // Delete employee-service assignments first
                $db->query("DELETE FROM employee_services WHERE employee_id = :id", [':id' => $id]);
                $result = $db->query("DELETE FROM employees WHERE id = :id", [':id' => $id]);
                break;

            case 'customers':
                $result = $db->query("DELETE FROM customers WHERE id = :id", [':id' => $id]);
                break;

            case 'reservations':
                $result = $db->query("DELETE FROM reservations WHERE id = :id", [':id' => $id]);
                break;

            default:
                $db->rollback();
                return ['success' => false, 'error' => 'Invalid table'];
        }

        if ($result !== false) {
            $db->commit();
            return ['success' => true, 'message' => 'Item deleted successfully'];
        } else {
            $db->rollback();
            return ['success' => false, 'error' => 'Failed to delete item'];
        }
    } catch (Exception $e) {
        $db->rollback();
        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
    }
}

/**
 * Delete specific entity (simplified version for specific entity types)
 */
function deleteSpecificEntity(array $data, Database $db, string $table): array
{
    $id = $data['id'] ?? '';

    if (!$id) {
        return ['success' => false, 'error' => 'Missing ID'];
    }

    // Validate table name for security
    $allowedTables = ['services', 'categories', 'employees', 'customers', 'reservations'];
    if (!in_array($table, $allowedTables)) {
        return ['success' => false, 'error' => 'Invalid table'];
    }

    // Check dependencies using AdminHelpers
    require_once __DIR__ . '/../core/AdminHelpers.php';
    $dependencyCheck = AdminHelpers::checkDeleteDependencies($db, $table, $id);

    if (!$dependencyCheck['can_delete']) {
        return ['success' => false, 'error' => $dependencyCheck['message']];
    }

    try {
        $db->beginTransaction();

        switch ($table) {
            case 'categories':
                $result = $db->query("DELETE FROM categories WHERE id = :id", [':id' => $id]);
                break;

            case 'services':
                // Delete employee-service assignments first
                $db->query("DELETE FROM employee_services WHERE service_id = :id", [':id' => $id]);
                $result = $db->query("DELETE FROM services WHERE id = :id", [':id' => $id]);
                break;

            case 'employees':
                // Delete employee-service assignments first
                $db->query("DELETE FROM employee_services WHERE employee_id = :id", [':id' => $id]);
                $result = $db->query("DELETE FROM employees WHERE id = :id", [':id' => $id]);
                break;

            case 'customers':
                $result = $db->query("DELETE FROM customers WHERE id = :id", [':id' => $id]);
                break;

            case 'reservations':
                $result = $db->query("DELETE FROM reservations WHERE id = :id", [':id' => $id]);
                break;

            default:
                $db->rollback();
                return ['success' => false, 'error' => 'Invalid table'];
        }

        if ($result !== false) {
            $db->commit();
            return ['success' => true, 'message' => ucfirst($table) . ' deleted successfully'];
        } else {
            $db->rollback();
            return ['success' => false, 'error' => 'Failed to delete ' . $table];
        }
    } catch (Exception $e) {
        $db->rollback();
        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
    }
}

/**
 * Assign service to category
 */
function assignServiceToCategory(array $data, Database $db): array
{
    $serviceId = $data['service_id'] ?? '';
    $categoryId = $data['category_id'] ?? '';

    if (!$serviceId || !$categoryId) {
        return ['success' => false, 'error' => 'Missing service ID or category ID'];
    }

    try {
        $result = $db->query(
            "UPDATE services SET category_id = :category_id WHERE id = :service_id",
            [':category_id' => $categoryId, ':service_id' => $serviceId]
        );

        if ($result !== false) {
            return ['success' => true, 'message' => 'Service assigned to category successfully'];
        } else {
            return ['success' => false, 'error' => 'Failed to assign service to category'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
    }
}

/**
 * Unassign service from category
 */
function unassignServiceFromCategory(array $data, Database $db): array
{
    $serviceId = $data['service_id'] ?? '';

    if (!$serviceId) {
        return ['success' => false, 'error' => 'Missing service ID'];
    }

    try {
        $result = $db->query(
            "UPDATE services SET category_id = NULL WHERE id = :service_id",
            [':service_id' => $serviceId]
        );

        if ($result !== false) {
            return ['success' => true, 'message' => 'Service unassigned from category successfully'];
        } else {
            return ['success' => false, 'error' => 'Failed to unassign service from category'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
    }
}

/**
 * Get form for editing/adding items
 */
function getForm(array $data, Database $db): array
{
    $type = $data['type'] ?? '';
    $id = $data['id'] ?? '';

    $allowedTypes = ['categories', 'services', 'employees', 'customers', 'reservations'];
    if (!in_array($type, $allowedTypes)) {
        return ['success' => false, 'error' => 'Invalid type'];
    }

    $formFile = __DIR__ . "/../views/forms/{$type}.php";
    if (!file_exists($formFile)) {
        return ['success' => false, 'error' => 'Form not found'];
    }

    // Get item data if editing
    $item = null;
    if ($id) {
        $item = $db->fetchRow("SELECT * FROM {$type} WHERE id = :id", [':id' => $id]);
        if (!$item) {
            return ['success' => false, 'error' => 'Item not found'];
        }
    }

    // Ensure AdminHelpers is available for form files
    if (!class_exists('AdminHelpers')) {
        require_once __DIR__ . '/../core/AdminHelpers.php';
    }

    ob_start();
    require $formFile;
    $form = ob_get_clean();

    // Ensure UTF-8 encoding
    $form = mb_convert_encoding($form, 'UTF-8', 'UTF-8');

    // Remove any invalid UTF-8 characters
    $form = mb_scrub($form, 'UTF-8');

    return ['success' => true, 'form' => $form];
}

/**
 * Get dashboard statistics
 */
function getDashboardStats(Database $db): array
{
    $stats = [
        'total_categories' => $db->fetchRow("SELECT COUNT(*) as count FROM categories")['count'],
        'total_services' => $db->fetchRow("SELECT COUNT(*) as count FROM services")['count'],
        'total_employees' => $db->fetchRow("SELECT COUNT(*) as count FROM employees")['count'],
        'total_customers' => $db->fetchRow("SELECT COUNT(*) as count FROM customers")['count'],
        'total_reservations' => $db->fetchRow("SELECT COUNT(*) as count FROM reservations")['count'],
        'active_services' => $db->fetchRow("SELECT COUNT(*) as count FROM services WHERE is_active = 1")['count'],
        'active_employees' => $db->fetchRow("SELECT COUNT(*) as count FROM employees WHERE is_active = 1")['count'],
        'todays_reservations' => $db->fetchRow("SELECT COUNT(*) as count FROM reservations WHERE date = :date", [':date' => date('Y-m-d')])['count'],
    ];

    return ['success' => true, 'stats' => $stats];
}

/**
 * Get category data
 */
function getCategoryData(array $data, Database $db): array
{
    $id = $data['id'] ?? '';

    if (!$id) {
        return ['success' => false, 'error' => 'ID required'];
    }

    $category = $db->fetchRow("SELECT * FROM categories WHERE id = :id", [':id' => $id]);

    if (!$category) {
        return ['success' => false, 'error' => 'Category not found'];
    }

    return ['success' => true, 'category' => $category];
}

/**
 * Get service data
 */
function getServiceData(array $data, Database $db): array
{
    $id = $data['id'] ?? '';

    if (!$id) {
        return ['success' => false, 'error' => 'ID required'];
    }

    $service = $db->fetchRow("SELECT * FROM services WHERE id = :id", [':id' => $id]);

    if (!$service) {
        return ['success' => false, 'error' => 'Service not found'];
    }

    // Get assigned employees
    $employees = $db->fetchAll(
        "SELECT e.id, e.name FROM employees e
         JOIN employee_services es ON e.id = es.employee_id
         WHERE es.service_id = :service_id",
        [':service_id' => $id]
    );

    $service['employees'] = $employees;

    return ['success' => true, 'service' => $service];
}

/**
 * Get employee data
 */
function getEmployeeData(array $data, Database $db): array
{
    $id = $data['id'] ?? '';

    if (!$id) {
        return ['success' => false, 'error' => 'ID required'];
    }

    $employee = $db->fetchRow("SELECT * FROM employees WHERE id = :id", [':id' => $id]);

    if (!$employee) {
        return ['success' => false, 'error' => 'Employee not found'];
    }

    // Get assigned services
    $services = $db->fetchAll(
        "SELECT s.id, s.name FROM services s
         JOIN employee_services es ON s.id = es.service_id
         WHERE es.employee_id = :employee_id",
        [':employee_id' => $id]
    );

    $employee['services'] = $services;

    return ['success' => true, 'employee' => $employee];
}

/**
 * Get customer data
 */
function getCustomerData(array $data, Database $db): array
{
    $id = $data['id'] ?? '';

    if (!$id) {
        return ['success' => false, 'error' => 'ID required'];
    }

    $customer = $db->fetchRow("SELECT * FROM customers WHERE id = :id", [':id' => $id]);

    if (!$customer) {
        return ['success' => false, 'error' => 'Customer not found'];
    }

    return ['success' => true, 'customer' => $customer];
}

/**
 * Get reservation data
 */
function getReservationData(array $data, Database $db): array
{
    $id = $data['id'] ?? '';

    if (!$id) {
        return ['success' => false, 'error' => 'ID required'];
    }

    $reservation = $db->fetchRow("
        SELECT r.*, c.name as customer_name, s.name as service_name, e.name as employee_name
        FROM reservations r
        LEFT JOIN customers c ON r.customer_id = c.id
        LEFT JOIN services s ON r.service_id = s.id
        LEFT JOIN employees e ON r.employee_id = e.id
        WHERE r.id = :id
    ", [':id' => $id]);

    if (!$reservation) {
        return ['success' => false, 'error' => 'Reservation not found'];
    }

    return ['success' => true, 'reservation' => $reservation];
}

/**
 * Save category
 */
function saveCategory(array $data, Database $db): array
{
    require_once __DIR__ . '/categories.php';
    // Convert AJAX action to controller action
    $data['action'] = 'save';
    return handleCategoriesForm($data, $db);
}

/**
 * Save service
 */
function saveService(array $data, Database $db): array
{
    require_once __DIR__ . '/services.php';
    // Convert AJAX action to controller action
    $data['action'] = 'save';
    return handleServicesForm($data, $db);
}

/**
 * Save employee
 */
function saveEmployee(array $data, Database $db): array
{
    require_once __DIR__ . '/employees.php';
    // Convert AJAX action to controller action
    $data['action'] = 'save';
    return handleEmployeesForm($data, $db);
}

/**
 * Save customer
 */
function saveCustomer(array $data, Database $db): array
{
    require_once __DIR__ . '/customers.php';
    // Convert AJAX action to controller action
    $data['action'] = 'save';
    return handleCustomersForm($data, $db);
}

/**
 * Save reservation
 */
function saveReservation(array $data, Database $db): array
{
    require_once __DIR__ . '/reservations.php';
    // Convert AJAX action to controller action
    $data['action'] = 'save';
    return handleReservationsForm($data, $db);
}

/**
 * Update reservation status
 */
function updateReservationStatus(array $data, Database $db): array
{
    $id = $data['id'] ?? '';
    $status = $data['status'] ?? '';

    if (!$id || !$status) {
        return ['success' => false, 'error' => 'Missing required data'];
    }

    // Validate status
    $validStatuses = ['pending', 'confirmed', 'completed', 'cancelled', 'no_show'];
    if (!in_array($status, $validStatuses)) {
        return ['success' => false, 'error' => 'Invalid status'];
    }

    try {
        $result = $db->query(
            "UPDATE reservations SET status = :status, updated_at = :updated_at WHERE id = :id",
            [
                ':id' => $id,
                ':status' => $status,
                ':updated_at' => date('Y-m-d H:i:s')
            ]
        );

        if ($result !== false) {
            return ['success' => true, 'message' => 'Reservation status updated successfully'];
        } else {
            return ['success' => false, 'error' => 'Failed to update reservation status'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
    }
}

/**
 * Bulk update reservation status
 */
function bulkUpdateReservationStatus(array $data, Database $db): array
{
    $idsJson = $data['ids'] ?? '';
    $status = $data['status'] ?? '';

    if (!$idsJson || !$status) {
        return ['success' => false, 'error' => 'Missing required data'];
    }

    $ids = json_decode($idsJson, true);
    if (!is_array($ids) || empty($ids)) {
        return ['success' => false, 'error' => 'Invalid IDs provided'];
    }

    // Validate status
    $validStatuses = ['pending', 'confirmed', 'completed', 'cancelled', 'no_show'];
    if (!in_array($status, $validStatuses)) {
        return ['success' => false, 'error' => 'Invalid status'];
    }

    try {
        $db->beginTransaction();

        $placeholders = str_repeat('?,', count($ids) - 1) . '?';
        $params = [$status, date('Y-m-d H:i:s')];
        foreach ($ids as $id) {
            $params[] = $id;
        }

        $result = $db->query(
            "UPDATE reservations SET status = ?, updated_at = ? WHERE id IN ($placeholders)",
            $params
        );

        if ($result !== false) {
            $db->commit();
            return ['success' => true, 'message' => 'Reservations updated successfully'];
        } else {
            $db->rollback();
            return ['success' => false, 'error' => 'Failed to update reservations'];
        }
    } catch (Exception $e) {
        if ($db->inTransaction()) {
            $db->rollback();
        }
        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
    }
}

/**
 * Get reservations for a specific day
 */
function getDayReservations(array $data, Database $db): array
{
    $date = $data['date'] ?? '';

    if (!$date) {
        return ['success' => false, 'error' => 'Missing date'];
    }

    try {
        $reservations = $db->fetchAll(
            "SELECT r.*,
                    c.name as customer_name,
                    s.name as service_name,
                    s.duration,
                    e.name as employee_name,
                    e.color as employee_color
             FROM reservations r
             JOIN customers c ON r.customer_id = c.id
             JOIN services s ON r.service_id = s.id
             LEFT JOIN employees e ON r.employee_id = e.id
             WHERE r.date = :date
             ORDER BY r.start_time",
            [':date' => $date]
        );

        return ['success' => true, 'reservations' => $reservations];
    } catch (Exception $e) {
        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
    }
}

/**
 * Duplicate a service
 */
function duplicateService(array $data, Database $db): array
{
    $id = $data['id'] ?? '';

    if (!$id) {
        return ['success' => false, 'error' => 'Missing service ID'];
    }

    try {
        // Get the original service
        $originalService = $db->fetchRow(
            "SELECT * FROM services WHERE id = :id",
            [':id' => $id]
        );

        if (!$originalService) {
            return ['success' => false, 'error' => 'Service not found'];
        }

        // Generate new ID for the duplicate
        $newId = 'SRV' . strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 8));

        // Create duplicate with modified name
        $duplicateName = $originalService['name'] . ' (Copy)';
        $duplicateNameEn = $originalService['name_en'] ? $originalService['name_en'] . ' (Copy)' : null;

        $db->beginTransaction();

        // Insert the duplicate service
        $result = $db->query(
            "INSERT INTO services (id, category_id, name, name_en, description, duration, price,
             employee_selection, preparation_time, cleanup_time, buffer_time, is_active, sort_order, created_at, updated_at)
             VALUES (:id, :category_id, :name, :name_en, :description, :duration, :price,
             :employee_selection, :preparation_time, :cleanup_time, :buffer_time, :is_active, :sort_order, :created_at, :updated_at)",
            [
                ':id' => $newId,
                ':category_id' => $originalService['category_id'],
                ':name' => $duplicateName,
                ':name_en' => $duplicateNameEn,
                ':description' => $originalService['description'],
                ':duration' => $originalService['duration'],
                ':price' => $originalService['price'],
                ':employee_selection' => $originalService['employee_selection'],
                ':preparation_time' => $originalService['preparation_time'],
                ':cleanup_time' => $originalService['cleanup_time'],
                ':buffer_time' => $originalService['buffer_time'],
                ':is_active' => 1, // Make duplicate active by default
                ':sort_order' => $originalService['sort_order'],
                ':created_at' => date('Y-m-d H:i:s'),
                ':updated_at' => date('Y-m-d H:i:s')
            ]
        );

        if ($result === false) {
            $db->rollback();
            return ['success' => false, 'error' => 'Failed to create duplicate service'];
        }

        // Copy employee-service relationships
        $employeeServices = $db->fetchAll(
            "SELECT employee_id FROM employee_services WHERE service_id = :service_id",
            [':service_id' => $id]
        );

        foreach ($employeeServices as $employeeService) {
            $db->query(
                "INSERT INTO employee_services (employee_id, service_id) VALUES (:employee_id, :service_id)",
                [
                    ':employee_id' => $employeeService['employee_id'],
                    ':service_id' => $newId
                ]
            );
        }

        $db->commit();

        return [
            'success' => true,
            'message' => 'Service duplicated successfully',
            'new_id' => $newId
        ];
    } catch (Exception $e) {
        if ($db->inTransaction()) {
            $db->rollback();
        }
        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
    }
}

/**
 * Bulk delete items
 */
function bulkDelete(array $data, Database $db): array
{
    $idsJson = $data['ids'] ?? '';
    $table = $data['table'] ?? '';

    if (!$idsJson || !$table) {
        return ['success' => false, 'error' => 'Missing required data'];
    }

    $ids = json_decode($idsJson, true);
    if (!is_array($ids) || empty($ids)) {
        return ['success' => false, 'error' => 'Invalid IDs provided'];
    }

    // Validate table name for security
    $allowedTables = ['services', 'categories', 'employees', 'customers', 'reservations'];
    if (!in_array($table, $allowedTables)) {
        return ['success' => false, 'error' => 'Invalid table'];
    }

    try {
        $db->beginTransaction();
        $deletedCount = 0;
        $errors = [];

        foreach ($ids as $id) {
            // Check dependencies for each item
            require_once __DIR__ . '/../core/AdminHelpers.php';
            $dependencyCheck = AdminHelpers::checkDeleteDependencies($db, $table, $id);

            if (!$dependencyCheck['can_delete']) {
                $errors[] = "Cannot delete item {$id}: {$dependencyCheck['message']}";
                continue;
            }

            // Handle cascading deletes based on table
            switch ($table) {
                case 'categories':
                    $result = $db->query("DELETE FROM categories WHERE id = :id", [':id' => $id]);
                    break;

                case 'services':
                    // Delete employee-service assignments first
                    $db->query("DELETE FROM employee_services WHERE service_id = :id", [':id' => $id]);
                    $result = $db->query("DELETE FROM services WHERE id = :id", [':id' => $id]);
                    break;

                case 'employees':
                    // Delete employee-service assignments first
                    $db->query("DELETE FROM employee_services WHERE employee_id = :id", [':id' => $id]);
                    $result = $db->query("DELETE FROM employees WHERE id = :id", [':id' => $id]);
                    break;

                case 'customers':
                    $result = $db->query("DELETE FROM customers WHERE id = :id", [':id' => $id]);
                    break;

                case 'reservations':
                    $result = $db->query("DELETE FROM reservations WHERE id = :id", [':id' => $id]);
                    break;

                default:
                    $errors[] = "Invalid table for item {$id}";
                    continue 2;
            }

            if ($result !== false) {
                $deletedCount++;
            } else {
                $errors[] = "Failed to delete item {$id}";
            }
        }

        if ($deletedCount > 0) {
            $db->commit();
            $message = "Successfully deleted {$deletedCount} item(s)";
            if (!empty($errors)) {
                $message .= ". Some items could not be deleted: " . implode(', ', $errors);
            }
            return ['success' => true, 'message' => $message, 'deleted_count' => $deletedCount];
        } else {
            $db->rollback();
            return ['success' => false, 'error' => 'No items could be deleted: ' . implode(', ', $errors)];
        }
    } catch (Exception $e) {
        if ($db->inTransaction()) {
            $db->rollback();
        }
        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
    }
}

/**
 * Bulk email customers
 */
function bulkEmail(array $data, Database $db): array
{
    $idsJson = $data['ids'] ?? '';
    $subject = $data['subject'] ?? '';
    $message = $data['message'] ?? '';
    $template = $data['template'] ?? 'custom';

    if (!$idsJson) {
        return ['success' => false, 'error' => 'Missing customer IDs'];
    }

    $ids = json_decode($idsJson, true);
    if (!is_array($ids) || empty($ids)) {
        return ['success' => false, 'error' => 'Invalid customer IDs provided'];
    }

    if ($template === 'custom' && (!$subject || !$message)) {
        return ['success' => false, 'error' => 'Subject and message are required for custom emails'];
    }

    try {
        // Get customer emails
        $placeholders = str_repeat('?,', count($ids) - 1) . '?';
        $customers = $db->fetchAll(
            "SELECT id, name, email FROM customers WHERE id IN ($placeholders) AND email IS NOT NULL AND email != ''",
            $ids
        );

        if (empty($customers)) {
            return ['success' => false, 'error' => 'No customers found with valid email addresses'];
        }

        $sentCount = 0;
        $errors = [];

        foreach ($customers as $customer) {
            // Prepare email content based on template
            $emailSubject = $subject;
            $emailMessage = $message;

            if ($template !== 'custom') {
                $templateData = getEmailTemplate($template, $customer, $db);
                if ($templateData) {
                    $emailSubject = $templateData['subject'];
                    $emailMessage = $templateData['message'];
                }
            }

            // Replace placeholders in message
            $emailMessage = str_replace(
                ['{{customer_name}}', '{{customer_email}}'],
                [$customer['name'], $customer['email']],
                $emailMessage
            );

            // Send email using SMTP class for consistency
            require_once __DIR__ . '/../../shared/smtp.php';
            $mailer = new SMTPMailer();

            if ($mailer->sendCustomEmail($customer['email'], $emailSubject, $emailMessage, $customer['name'], $customer['id'])) {
                $sentCount++;
            } else {
                $errors[] = "Failed to send email to {$customer['name']} ({$customer['email']})";
            }
        }

        $message = "Successfully sent {$sentCount} email(s)";
        if (!empty($errors)) {
            $message .= ". Some emails failed: " . implode(', ', $errors);
        }

        return ['success' => true, 'message' => $message, 'sent_count' => $sentCount];
    } catch (Exception $e) {
        return ['success' => false, 'error' => 'Error sending emails: ' . $e->getMessage()];
    }
}

/**
 * Bulk SMS customers
 */
function bulkSMS(array $data, Database $db): array
{
    $idsJson = $data['ids'] ?? '';
    $message = $data['message'] ?? '';
    $template = $data['template'] ?? 'custom';

    if (!$idsJson) {
        return ['success' => false, 'error' => 'Missing customer IDs'];
    }

    $ids = json_decode($idsJson, true);
    if (!is_array($ids) || empty($ids)) {
        return ['success' => false, 'error' => 'Invalid customer IDs provided'];
    }

    if ($template === 'custom' && !$message) {
        return ['success' => false, 'error' => 'Message is required for custom SMS'];
    }

    try {
        // Get customer phone numbers
        $placeholders = str_repeat('?,', count($ids) - 1) . '?';
        $customers = $db->fetchAll(
            "SELECT id, name, phone FROM customers WHERE id IN ($placeholders) AND phone IS NOT NULL AND phone != ''",
            $ids
        );

        if (empty($customers)) {
            return ['success' => false, 'error' => 'No customers found with valid phone numbers'];
        }

        $sentCount = 0;
        $errors = [];

        foreach ($customers as $customer) {
            // Prepare SMS content based on template
            $smsMessage = $message;

            if ($template !== 'custom') {
                $templateData = getSMSTemplate($template, $customer, $db);
                if ($templateData) {
                    $smsMessage = $templateData['message'];
                }
            }

            // Replace placeholders in message
            $smsMessage = str_replace(
                ['{{customer_name}}', '{{customer_phone}}'],
                [$customer['name'], $customer['phone']],
                $smsMessage
            );

            // Send SMS (you'll need to implement your SMS sending logic here)
            if (sendSMS($customer['phone'], $smsMessage)) {
                $sentCount++;

                // Log SMS sent
                $db->query(
                    "INSERT INTO sms_log (customer_id, message, sent_at) VALUES (:customer_id, :message, :sent_at)",
                    [
                        ':customer_id' => $customer['id'],
                        ':message' => $smsMessage,
                        ':sent_at' => date('Y-m-d H:i:s')
                    ]
                );
            } else {
                $errors[] = "Failed to send SMS to {$customer['name']} ({$customer['phone']})";
            }
        }

        $message = "Successfully sent {$sentCount} SMS(es)";
        if (!empty($errors)) {
            $message .= ". Some SMS failed: " . implode(', ', $errors);
        }

        return ['success' => true, 'message' => $message, 'sent_count' => $sentCount];
    } catch (Exception $e) {
        return ['success' => false, 'error' => 'Error sending SMS: ' . $e->getMessage()];
    }
}

/**
 * Bulk export data
 */
function bulkExport(array $data, Database $db): array
{
    $idsJson = $data['ids'] ?? '';
    $table = $data['table'] ?? '';
    $format = $data['format'] ?? 'csv';

    if (!$idsJson || !$table) {
        return ['success' => false, 'error' => 'Missing required data'];
    }

    $ids = json_decode($idsJson, true);
    if (!is_array($ids) || empty($ids)) {
        return ['success' => false, 'error' => 'Invalid IDs provided'];
    }

    // Validate table name for security
    $allowedTables = ['services', 'categories', 'employees', 'customers', 'reservations'];
    if (!in_array($table, $allowedTables)) {
        return ['success' => false, 'error' => 'Invalid table'];
    }

    // Validate format
    $allowedFormats = ['csv', 'excel', 'json'];
    if (!in_array($format, $allowedFormats)) {
        return ['success' => false, 'error' => 'Invalid export format'];
    }

    try {
        // Get data based on table
        $placeholders = str_repeat('?,', count($ids) - 1) . '?';

        switch ($table) {
            case 'customers':
                $data = $db->fetchAll(
                    "SELECT id, name, email, phone, address, created_at FROM customers WHERE id IN ($placeholders)",
                    $ids
                );
                break;

            case 'services':
                $data = $db->fetchAll(
                    "SELECT s.id, s.name, s.description, s.duration, s.price, s.is_active, c.name as category_name
                     FROM services s
                     LEFT JOIN categories c ON s.category_id = c.id
                     WHERE s.id IN ($placeholders)",
                    $ids
                );
                break;

            case 'employees':
                $data = $db->fetchAll(
                    "SELECT id, name, email, phone, position, is_active, created_at FROM employees WHERE id IN ($placeholders)",
                    $ids
                );
                break;

            case 'categories':
                $data = $db->fetchAll(
                    "SELECT id, name, description, color, sort_order FROM categories WHERE id IN ($placeholders)",
                    $ids
                );
                break;

            case 'reservations':
                $data = $db->fetchAll(
                    "SELECT r.id, r.date, r.start_time, r.status, c.name as customer_name, s.name as service_name, e.name as employee_name, r.notes
                     FROM reservations r
                     LEFT JOIN customers c ON r.customer_id = c.id
                     LEFT JOIN services s ON r.service_id = s.id
                     LEFT JOIN employees e ON r.employee_id = e.id
                     WHERE r.id IN ($placeholders)",
                    $ids
                );
                break;
        }

        if (empty($data)) {
            return ['success' => false, 'error' => 'No data found for export'];
        }

        // Generate export file
        $filename = $table . '_export_' . date('Y-m-d_H-i-s');
        $exportResult = generateExportFile($data, $format, $filename);

        if ($exportResult['success']) {
            return [
                'success' => true,
                'message' => "Successfully exported {$exportResult['count']} records",
                'download_url' => $exportResult['download_url'],
                'filename' => $exportResult['filename']
            ];
        } else {
            return ['success' => false, 'error' => $exportResult['error']];
        }
    } catch (Exception $e) {
        return ['success' => false, 'error' => 'Error exporting data: ' . $e->getMessage()];
    }
}

/**
 * Bulk update service prices
 */
function bulkUpdatePrices(array $data, Database $db): array
{
    $idsJson = $data['ids'] ?? '';
    $updateType = $data['update_type'] ?? 'percentage'; // percentage or fixed
    $value = floatval($data['value'] ?? 0);

    if (!$idsJson || $value == 0) {
        return ['success' => false, 'error' => 'Missing required data'];
    }

    $ids = json_decode($idsJson, true);
    if (!is_array($ids) || empty($ids)) {
        return ['success' => false, 'error' => 'Invalid service IDs provided'];
    }

    if (!in_array($updateType, ['percentage', 'fixed'])) {
        return ['success' => false, 'error' => 'Invalid update type'];
    }

    try {
        $db->beginTransaction();
        $updatedCount = 0;

        foreach ($ids as $id) {
            // Get current price
            $service = $db->fetchRow("SELECT price FROM services WHERE id = :id", [':id' => $id]);
            if (!$service) {
                continue;
            }

            $currentPrice = floatval($service['price']);
            $newPrice = $currentPrice;

            if ($updateType === 'percentage') {
                $newPrice = $currentPrice * (1 + ($value / 100));
            } else {
                $newPrice = $currentPrice + $value;
            }

            // Ensure price is not negative
            $newPrice = max(0, $newPrice);

            $result = $db->query(
                "UPDATE services SET price = :price, updated_at = :updated_at WHERE id = :id",
                [
                    ':id' => $id,
                    ':price' => number_format($newPrice, 2, '.', ''),
                    ':updated_at' => date('Y-m-d H:i:s')
                ]
            );

            if ($result !== false) {
                $updatedCount++;
            }
        }

        if ($updatedCount > 0) {
            $db->commit();
            return ['success' => true, 'message' => "Successfully updated prices for {$updatedCount} service(s)"];
        } else {
            $db->rollback();
            return ['success' => false, 'error' => 'No services could be updated'];
        }
    } catch (Exception $e) {
        if ($db->inTransaction()) {
            $db->rollback();
        }
        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
    }
}

/**
 * Bulk duplicate items
 */
function bulkDuplicate(array $data, Database $db): array
{
    $idsJson = $data['ids'] ?? '';
    $table = $data['table'] ?? '';

    if (!$idsJson || !$table) {
        return ['success' => false, 'error' => 'Missing required data'];
    }

    $ids = json_decode($idsJson, true);
    if (!is_array($ids) || empty($ids)) {
        return ['success' => false, 'error' => 'Invalid IDs provided'];
    }

    // Only services and categories support duplication for now
    $allowedTables = ['services', 'categories'];
    if (!in_array($table, $allowedTables)) {
        return ['success' => false, 'error' => 'Duplication not supported for this type'];
    }

    try {
        $db->beginTransaction();
        $duplicatedCount = 0;

        foreach ($ids as $id) {
            if ($table === 'services') {
                $result = duplicateServiceItem($id, $db);
            } else if ($table === 'categories') {
                $result = duplicateCategoryItem($id, $db);
            }

            if ($result) {
                $duplicatedCount++;
            }
        }

        if ($duplicatedCount > 0) {
            $db->commit();
            return ['success' => true, 'message' => "Successfully duplicated {$duplicatedCount} item(s)"];
        } else {
            $db->rollback();
            return ['success' => false, 'error' => 'No items could be duplicated'];
        }
    } catch (Exception $e) {
        if ($db->inTransaction()) {
            $db->rollback();
        }
        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
    }
}

/**
 * Bulk toggle status
 */
function bulkToggleStatus(array $data, Database $db): array
{
    $idsJson = $data['ids'] ?? '';
    $table = $data['table'] ?? '';

    if (!$idsJson || !$table) {
        return ['success' => false, 'error' => 'Missing required data'];
    }

    $ids = json_decode($idsJson, true);
    if (!is_array($ids) || empty($ids)) {
        return ['success' => false, 'error' => 'Invalid IDs provided'];
    }

    // Only services and employees have status fields
    $allowedTables = ['services', 'employees'];
    if (!in_array($table, $allowedTables)) {
        return ['success' => false, 'error' => 'Status toggle not supported for this type'];
    }

    try {
        $db->beginTransaction();
        $updatedCount = 0;

        foreach ($ids as $id) {
            // Get current status
            $item = $db->fetchRow("SELECT is_active FROM {$table} WHERE id = :id", [':id' => $id]);
            if (!$item) {
                continue;
            }

            $newStatus = $item['is_active'] ? 0 : 1;

            $result = $db->query(
                "UPDATE {$table} SET is_active = :status, updated_at = :updated_at WHERE id = :id",
                [
                    ':id' => $id,
                    ':status' => $newStatus,
                    ':updated_at' => date('Y-m-d H:i:s')
                ]
            );

            if ($result !== false) {
                $updatedCount++;
            }
        }

        if ($updatedCount > 0) {
            $db->commit();
            return ['success' => true, 'message' => "Successfully toggled status for {$updatedCount} item(s)"];
        } else {
            $db->rollback();
            return ['success' => false, 'error' => 'No items could be updated'];
        }
    } catch (Exception $e) {
        if ($db->inTransaction()) {
            $db->rollback();
        }
        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
    }
}

/**
 * Bulk update category colors
 */
function bulkUpdateColors(array $data, Database $db): array
{
    $idsJson = $data['ids'] ?? '';
    $color = $data['color'] ?? '';

    if (!$idsJson || !$color) {
        return ['success' => false, 'error' => 'Missing required data'];
    }

    $ids = json_decode($idsJson, true);
    if (!is_array($ids) || empty($ids)) {
        return ['success' => false, 'error' => 'Invalid category IDs provided'];
    }

    // Validate color format (hex color)
    if (!preg_match('/^#[a-fA-F0-9]{6}$/', $color)) {
        return ['success' => false, 'error' => 'Invalid color format'];
    }

    try {
        $db->beginTransaction();
        $updatedCount = 0;

        foreach ($ids as $id) {
            $result = $db->query(
                "UPDATE categories SET color = :color, updated_at = :updated_at WHERE id = :id",
                [
                    ':id' => $id,
                    ':color' => $color,
                    ':updated_at' => date('Y-m-d H:i:s')
                ]
            );

            if ($result !== false) {
                $updatedCount++;
            }
        }

        if ($updatedCount > 0) {
            $db->commit();
            return ['success' => true, 'message' => "Successfully updated colors for {$updatedCount} category(ies)"];
        } else {
            $db->rollback();
            return ['success' => false, 'error' => 'No categories could be updated'];
        }
    } catch (Exception $e) {
        if ($db->inTransaction()) {
            $db->rollback();
        }
        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
    }
}

/**
 * Bulk reorder categories
 */
function bulkReorder(array $data, Database $db): array
{
    $orderData = $data['order'] ?? '';

    if (!$orderData) {
        return ['success' => false, 'error' => 'Missing order data'];
    }

    $order = json_decode($orderData, true);
    if (!is_array($order) || empty($order)) {
        return ['success' => false, 'error' => 'Invalid order data provided'];
    }

    try {
        $db->beginTransaction();
        $updatedCount = 0;

        foreach ($order as $index => $categoryId) {
            $sortOrder = $index + 1;
            $result = $db->query(
                "UPDATE categories SET sort_order = :sort_order, updated_at = :updated_at WHERE id = :id",
                [
                    ':id' => $categoryId,
                    ':sort_order' => $sortOrder,
                    ':updated_at' => date('Y-m-d H:i:s')
                ]
            );

            if ($result !== false) {
                $updatedCount++;
            }
        }

        if ($updatedCount > 0) {
            $db->commit();
            return ['success' => true, 'message' => "Successfully reordered {$updatedCount} category(ies)"];
        } else {
            $db->rollback();
            return ['success' => false, 'error' => 'No categories could be reordered'];
        }
    } catch (Exception $e) {
        if ($db->inTransaction()) {
            $db->rollback();
        }
        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
    }
}

/**
 * Bulk schedule employees
 */
function bulkSchedule(array $data, Database $db): array
{
    $idsJson = $data['ids'] ?? '';
    $scheduleData = $data['schedule'] ?? '';

    if (!$idsJson || !$scheduleData) {
        return ['success' => false, 'error' => 'Missing required data'];
    }

    $ids = json_decode($idsJson, true);
    if (!is_array($ids) || empty($ids)) {
        return ['success' => false, 'error' => 'Invalid employee IDs provided'];
    }

    $schedule = json_decode($scheduleData, true);
    if (!is_array($schedule)) {
        return ['success' => false, 'error' => 'Invalid schedule data provided'];
    }

    try {
        $db->beginTransaction();
        $updatedCount = 0;

        foreach ($ids as $employeeId) {
            // Update employee schedule (assuming you have a schedule table or field)
            $result = $db->query(
                "UPDATE employees SET schedule = :schedule, updated_at = :updated_at WHERE id = :id",
                [
                    ':id' => $employeeId,
                    ':schedule' => json_encode($schedule),
                    ':updated_at' => date('Y-m-d H:i:s')
                ]
            );

            if ($result !== false) {
                $updatedCount++;
            }
        }

        if ($updatedCount > 0) {
            $db->commit();
            return ['success' => true, 'message' => "Successfully updated schedule for {$updatedCount} employee(s)"];
        } else {
            $db->rollback();
            return ['success' => false, 'error' => 'No employees could be updated'];
        }
    } catch (Exception $e) {
        if ($db->inTransaction()) {
            $db->rollback();
        }
        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
    }
}

/**
 * Bulk notify employees
 */
function bulkNotify(array $data, Database $db): array
{
    $idsJson = $data['ids'] ?? '';
    $message = $data['message'] ?? '';
    $type = $data['type'] ?? 'general';

    if (!$idsJson || !$message) {
        return ['success' => false, 'error' => 'Missing required data'];
    }

    $ids = json_decode($idsJson, true);
    if (!is_array($ids) || empty($ids)) {
        return ['success' => false, 'error' => 'Invalid employee IDs provided'];
    }

    try {
        // Get employee contact information
        $placeholders = str_repeat('?,', count($ids) - 1) . '?';
        $employees = $db->fetchAll(
            "SELECT id, name, email, phone FROM employees WHERE id IN ($placeholders) AND is_active = 1",
            $ids
        );

        if (empty($employees)) {
            return ['success' => false, 'error' => 'No active employees found'];
        }

        $notifiedCount = 0;
        $errors = [];

        foreach ($employees as $employee) {
            // Send notification (email and/or SMS based on availability)
            $notificationSent = false;

            if ($employee['email']) {
                $subject = "Notification: " . ucfirst($type);
                if (sendEmail($employee['email'], $subject, $message)) {
                    $notificationSent = true;
                }
            }

            if ($employee['phone']) {
                if (sendSMS($employee['phone'], $message)) {
                    $notificationSent = true;
                }
            }

            if ($notificationSent) {
                $notifiedCount++;

                // Log notification
                $db->query(
                    "INSERT INTO employee_notifications (employee_id, type, message, sent_at) VALUES (:employee_id, :type, :message, :sent_at)",
                    [
                        ':employee_id' => $employee['id'],
                        ':type' => $type,
                        ':message' => $message,
                        ':sent_at' => date('Y-m-d H:i:s')
                    ]
                );
            } else {
                $errors[] = "Failed to notify {$employee['name']}";
            }
        }

        $message = "Successfully notified {$notifiedCount} employee(s)";
        if (!empty($errors)) {
            $message .= ". Some notifications failed: " . implode(', ', $errors);
        }

        return ['success' => true, 'message' => $message, 'notified_count' => $notifiedCount];
    } catch (Exception $e) {
        return ['success' => false, 'error' => 'Error sending notifications: ' . $e->getMessage()];
    }
}

/**
 * Bulk remind customers about reservations
 */
function bulkRemind(array $data, Database $db): array
{
    $idsJson = $data['ids'] ?? '';
    $reminderType = $data['reminder_type'] ?? 'both'; // email, sms, or both

    if (!$idsJson) {
        return ['success' => false, 'error' => 'Missing reservation IDs'];
    }

    $ids = json_decode($idsJson, true);
    if (!is_array($ids) || empty($ids)) {
        return ['success' => false, 'error' => 'Invalid reservation IDs provided'];
    }

    try {
        // Get reservation details with customer information
        $placeholders = str_repeat('?,', count($ids) - 1) . '?';
        $reservations = $db->fetchAll(
            "SELECT r.*, c.name as customer_name, c.email, c.phone, s.name as service_name, e.name as employee_name
             FROM reservations r
             JOIN customers c ON r.customer_id = c.id
             JOIN services s ON r.service_id = s.id
             LEFT JOIN employees e ON r.employee_id = e.id
             WHERE r.id IN ($placeholders) AND r.status IN ('pending', 'confirmed')",
            $ids
        );

        if (empty($reservations)) {
            return ['success' => false, 'error' => 'No valid reservations found for reminders'];
        }

        $sentCount = 0;
        $errors = [];

        foreach ($reservations as $reservation) {
            $reminderSent = false;

            // Prepare reminder message
            $message = "Reminder: You have an appointment for {$reservation['service_name']} on {$reservation['date']} at {$reservation['start_time']}";
            if ($reservation['employee_name']) {
                $message .= " with {$reservation['employee_name']}";
            }

            // Send email reminder
            if (($reminderType === 'email' || $reminderType === 'both') && $reservation['email']) {
                $subject = "Appointment Reminder - {$reservation['service_name']}";
                if (sendEmail($reservation['email'], $subject, $message)) {
                    $reminderSent = true;
                }
            }

            // Send SMS reminder
            if (($reminderType === 'sms' || $reminderType === 'both') && $reservation['phone']) {
                if (sendSMS($reservation['phone'], $message)) {
                    $reminderSent = true;
                }
            }

            if ($reminderSent) {
                $sentCount++;

                // Log reminder sent
                $db->query(
                    "INSERT INTO reminder_log (reservation_id, customer_id, type, message, sent_at) VALUES (:reservation_id, :customer_id, :type, :message, :sent_at)",
                    [
                        ':reservation_id' => $reservation['id'],
                        ':customer_id' => $reservation['customer_id'],
                        ':type' => $reminderType,
                        ':message' => $message,
                        ':sent_at' => date('Y-m-d H:i:s')
                    ]
                );
            } else {
                $errors[] = "Failed to send reminder for reservation {$reservation['id']}";
            }
        }

        $message = "Successfully sent {$sentCount} reminder(s)";
        if (!empty($errors)) {
            $message .= ". Some reminders failed: " . implode(', ', $errors);
        }

        return ['success' => true, 'message' => $message, 'sent_count' => $sentCount];
    } catch (Exception $e) {
        return ['success' => false, 'error' => 'Error sending reminders: ' . $e->getMessage()];
    }
}

/**
 * Bulk reschedule reservations
 */
function bulkReschedule(array $data, Database $db): array
{
    $idsJson = $data['ids'] ?? '';
    $newDate = $data['new_date'] ?? '';
    $timeOffset = $data['time_offset'] ?? 0; // minutes to add/subtract

    if (!$idsJson || !$newDate) {
        return ['success' => false, 'error' => 'Missing required data'];
    }

    $ids = json_decode($idsJson, true);
    if (!is_array($ids) || empty($ids)) {
        return ['success' => false, 'error' => 'Invalid reservation IDs provided'];
    }

    // Validate date format
    if (!DateTime::createFromFormat('Y-m-d', $newDate)) {
        return ['success' => false, 'error' => 'Invalid date format'];
    }

    try {
        $db->beginTransaction();
        $rescheduledCount = 0;
        $errors = [];

        foreach ($ids as $id) {
            // Get current reservation
            $reservation = $db->fetchRow(
                "SELECT * FROM reservations WHERE id = :id AND status IN ('pending', 'confirmed')",
                [':id' => $id]
            );

            if (!$reservation) {
                $errors[] = "Reservation {$id} not found or cannot be rescheduled";
                continue;
            }

            // Calculate new time if offset is provided
            $newTime = $reservation['start_time'];
            if ($timeOffset != 0) {
                $time = new DateTime($reservation['start_time']);
                $time->modify("{$timeOffset} minutes");
                $newTime = $time->format('H:i:s');
            }

            // Check availability for new date/time (basic check)
            $conflictCheck = $db->fetchRow(
                "SELECT id FROM reservations WHERE date = :date AND start_time = :time AND employee_id = :employee_id AND id != :id AND status IN ('pending', 'confirmed')",
                [
                    ':date' => $newDate,
                    ':time' => $newTime,
                    ':employee_id' => $reservation['employee_id'],
                    ':id' => $id
                ]
            );

            if ($conflictCheck) {
                $errors[] = "Time conflict for reservation {$id}";
                continue;
            }

            // Update reservation
            $result = $db->query(
                "UPDATE reservations SET date = :date, start_time = :time, updated_at = :updated_at WHERE id = :id",
                [
                    ':id' => $id,
                    ':date' => $newDate,
                    ':time' => $newTime,
                    ':updated_at' => date('Y-m-d H:i:s')
                ]
            );

            if ($result !== false) {
                $rescheduledCount++;
            } else {
                $errors[] = "Failed to reschedule reservation {$id}";
            }
        }

        if ($rescheduledCount > 0) {
            $db->commit();
            $message = "Successfully rescheduled {$rescheduledCount} reservation(s)";
            if (!empty($errors)) {
                $message .= ". Some reservations could not be rescheduled: " . implode(', ', $errors);
            }
            return ['success' => true, 'message' => $message, 'rescheduled_count' => $rescheduledCount];
        } else {
            $db->rollback();
            return ['success' => false, 'error' => 'No reservations could be rescheduled: ' . implode(', ', $errors)];
        }
    } catch (Exception $e) {
        if ($db->inTransaction()) {
            $db->rollback();
        }
        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
    }
}

// Helper functions for bulk operations

/**
 * Duplicate a service item (helper for bulk duplicate)
 */
function duplicateServiceItem($id, Database $db): bool
{
    try {
        $originalService = $db->fetchRow("SELECT * FROM services WHERE id = :id", [':id' => $id]);
        if (!$originalService) {
            return false;
        }

        $newId = 'SRV' . strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 8));
        $duplicateName = $originalService['name'] . ' (Copy)';
        $duplicateNameEn = $originalService['name_en'] ? $originalService['name_en'] . ' (Copy)' : null;

        $result = $db->query(
            "INSERT INTO services (id, category_id, name, name_en, description, duration, price,
             employee_selection, preparation_time, cleanup_time, buffer_time, is_active, sort_order, created_at, updated_at)
             VALUES (:id, :category_id, :name, :name_en, :description, :duration, :price,
             :employee_selection, :preparation_time, :cleanup_time, :buffer_time, :is_active, :sort_order, :created_at, :updated_at)",
            [
                ':id' => $newId,
                ':category_id' => $originalService['category_id'],
                ':name' => $duplicateName,
                ':name_en' => $duplicateNameEn,
                ':description' => $originalService['description'],
                ':duration' => $originalService['duration'],
                ':price' => $originalService['price'],
                ':employee_selection' => $originalService['employee_selection'],
                ':preparation_time' => $originalService['preparation_time'],
                ':cleanup_time' => $originalService['cleanup_time'],
                ':buffer_time' => $originalService['buffer_time'],
                ':is_active' => 1,
                ':sort_order' => $originalService['sort_order'],
                ':created_at' => date('Y-m-d H:i:s'),
                ':updated_at' => date('Y-m-d H:i:s')
            ]
        );

        if ($result !== false) {
            // Copy employee-service relationships
            $employeeServices = $db->fetchAll(
                "SELECT employee_id FROM employee_services WHERE service_id = :service_id",
                [':service_id' => $id]
            );

            foreach ($employeeServices as $employeeService) {
                $db->query(
                    "INSERT INTO employee_services (employee_id, service_id) VALUES (:employee_id, :service_id)",
                    [
                        ':employee_id' => $employeeService['employee_id'],
                        ':service_id' => $newId
                    ]
                );
            }
            return true;
        }

        return false;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Duplicate a category item (helper for bulk duplicate)
 */
function duplicateCategoryItem($id, Database $db): bool
{
    try {
        $originalCategory = $db->fetchRow("SELECT * FROM categories WHERE id = :id", [':id' => $id]);
        if (!$originalCategory) {
            return false;
        }

        $newId = 'CAT' . strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 8));
        $duplicateName = $originalCategory['name'] . ' (Copy)';
        $duplicateNameEn = $originalCategory['name_en'] ? $originalCategory['name_en'] . ' (Copy)' : null;

        $result = $db->query(
            "INSERT INTO categories (id, name, name_en, description, color, sort_order, created_at, updated_at)
             VALUES (:id, :name, :name_en, :description, :color, :sort_order, :created_at, :updated_at)",
            [
                ':id' => $newId,
                ':name' => $duplicateName,
                ':name_en' => $duplicateNameEn,
                ':description' => $originalCategory['description'],
                ':color' => $originalCategory['color'],
                ':sort_order' => $originalCategory['sort_order'],
                ':created_at' => date('Y-m-d H:i:s'),
                ':updated_at' => date('Y-m-d H:i:s')
            ]
        );

        return $result !== false;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Get email template
 */
function getEmailTemplate($template, $customer, Database $db): ?array
{
    $templates = [
        'welcome' => [
            'subject' => 'Welcome to our service!',
            'message' => 'Dear {{customer_name}}, welcome to our service! We look forward to serving you.'
        ],
        'reminder' => [
            'subject' => 'Appointment Reminder',
            'message' => 'Dear {{customer_name}}, this is a reminder about your upcoming appointment.'
        ],
        'promotion' => [
            'subject' => 'Special Offer Just for You!',
            'message' => 'Dear {{customer_name}}, we have a special offer just for you!'
        ]
    ];

    return $templates[$template] ?? null;
}

/**
 * Get SMS template
 */
function getSMSTemplate($template, $customer, Database $db): ?array
{
    $templates = [
        'reminder' => [
            'message' => 'Hi {{customer_name}}, reminder about your appointment. See you soon!'
        ],
        'promotion' => [
            'message' => 'Hi {{customer_name}}, special offer available! Contact us for details.'
        ],
        'confirmation' => [
            'message' => 'Hi {{customer_name}}, your appointment has been confirmed. Thank you!'
        ]
    ];

    return $templates[$template] ?? null;
}

/**
 * Send email using the existing SMTP system
 */
function sendEmail($to, $subject, $message): bool
{
    try {
        // Include the SMTP class
        require_once __DIR__ . '/../../shared/smtp.php';

        // Create SMTPMailer instance
        $smtp = new SMTPMailer();

        // Convert plain text to HTML if needed
        if (strpos($message, '<') === false) {
            $htmlMessage = nl2br(htmlspecialchars($message));
        } else {
            $htmlMessage = $message;
        }

        // Get business name for email template
        $businessName = Application::getSetting('business_name', 'Your Business');

        // Create HTML email template
        $emailBody = "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>{$subject}</title>
        </head>
        <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                <div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px;'>
                    <h2 style='color: #007bff; margin: 0;'>{$businessName}</h2>
                </div>
                <div style='background: white; padding: 20px; border-radius: 5px; border: 1px solid #dee2e6;'>
                    {$htmlMessage}
                </div>
                <div style='margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px; font-size: 12px; color: #6c757d;'>
                    <p>This email was sent from {$businessName}. If you received this email in error, please ignore it.</p>
                </div>
            </div>
        </body>
        </html>";

        // Send email using SMTPMailer class (which handles SMTP vs PHP mail automatically)
        $result = $smtp->send($to, $subject, $emailBody, true);

        return $result;
    } catch (Exception $e) {
        error_log("Email sending failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Send SMS using configured SMS provider
 */
function sendSMS($to, $message): bool
{
    try {
        // Get SMS provider settings
        $smsProvider = Application::getSetting('sms_provider', 'none');
        $smsEnabled = Application::getSetting('sms_enabled', '0');


        // Debug logging (both error_log and file)
        error_log("sendSMS called - To: $to, Message: $message");
        error_log("SMS Debug - Enabled: $smsEnabled, Provider: $smsProvider, To: $to");

        // Also log to file for debugging
        file_put_contents(
            __DIR__ . '/../sms_debug.log',
            date('Y-m-d H:i:s') . " - sendSMS called - To: $to, Message: $message\n" .
                date('Y-m-d H:i:s') . " - SMS Config - Enabled: $smsEnabled, Provider: $smsProvider\n",
            FILE_APPEND | LOCK_EX
        );

        if (!$smsEnabled || $smsProvider === 'none') {
            error_log("SMS is disabled or no provider configured");
            return false;
        }



        // Clean phone number (remove spaces, dashes, etc.)
        $cleanPhone = preg_replace('/[^\d+]/', '', $to);

        // Ensure phone number starts with + for international format
        if (!str_starts_with($cleanPhone, '+')) {
            $defaultCountryCode = Application::getSetting('default_country_code', '+30');
            $cleanPhone = $defaultCountryCode . ltrim($cleanPhone, '0');
        }

        // Send SMS based on provider
        file_put_contents(
            __DIR__ . '/../sms_debug.log',
            date('Y-m-d H:i:s') . " - About to call provider function - Provider: $smsProvider, CleanPhone: $cleanPhone\n",
            FILE_APPEND | LOCK_EX
        );

        switch ($smsProvider) {
            case 'twilio':
                file_put_contents(__DIR__ . '/../sms_debug.log', date('Y-m-d H:i:s') . " - Calling sendSMSViaTwilio\n", FILE_APPEND | LOCK_EX);
                return sendSMSViaTwilio($cleanPhone, $message);

            case 'nexmo':
                file_put_contents(__DIR__ . '/../sms_debug.log', date('Y-m-d H:i:s') . " - Calling sendSMSViaNexmo\n", FILE_APPEND | LOCK_EX);
                return sendSMSViaNexmo($cleanPhone, $message);

            case 'apifon':
                file_put_contents(__DIR__ . '/../sms_debug.log', date('Y-m-d H:i:s') . " - Calling sendSMSViaApifon\n", FILE_APPEND | LOCK_EX);
                return sendSMSViaApifon($cleanPhone, $message);

            case 'smstools':
                file_put_contents(__DIR__ . '/../sms_debug.log', date('Y-m-d H:i:s') . " - Calling sendSMSViaSMStools\n", FILE_APPEND | LOCK_EX);
                return sendSMSViaSMStools($cleanPhone, $message);

            case 'smsto':
                file_put_contents(__DIR__ . '/../sms_debug.log', date('Y-m-d H:i:s') . " - Calling sendSMSViaSMSto\n", FILE_APPEND | LOCK_EX);
                return sendSMSViaSMSto($cleanPhone, $message);

            case 'custom':
                file_put_contents(__DIR__ . '/../sms_debug.log', date('Y-m-d H:i:s') . " - Calling sendSMSViaCustom\n", FILE_APPEND | LOCK_EX);
                return sendSMSViaCustom($cleanPhone, $message);

            default:
                error_log("Unknown SMS provider: $smsProvider");
                file_put_contents(__DIR__ . '/../sms_debug.log', date('Y-m-d H:i:s') . " - Unknown SMS provider: $smsProvider\n", FILE_APPEND | LOCK_EX);
                return false;
        }
    } catch (Exception $e) {
        error_log("SMS sending failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Generate export file
 */
function generateExportFile($data, $format, $filename): array
{
    try {
        $exportDir = __DIR__ . '/../exports/';
        if (!is_dir($exportDir)) {
            mkdir($exportDir, 0755, true);
        }

        switch ($format) {
            case 'csv':
                return generateCSVExport($data, $exportDir, $filename);
            case 'excel':
                return generateExcelExport($data, $exportDir, $filename);
            case 'json':
                return generateJSONExport($data, $exportDir, $filename);
            default:
                return ['success' => false, 'error' => 'Unsupported export format'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'error' => 'Export generation failed: ' . $e->getMessage()];
    }
}

/**
 * Generate CSV export
 */
function generateCSVExport($data, $exportDir, $filename): array
{
    $filepath = $exportDir . $filename . '.csv';
    $file = fopen($filepath, 'w');

    if (!$file) {
        return ['success' => false, 'error' => 'Could not create export file'];
    }

    // Write headers
    if (!empty($data)) {
        fputcsv($file, array_keys($data[0]));

        // Write data
        foreach ($data as $row) {
            fputcsv($file, $row);
        }
    }

    fclose($file);

    return [
        'success' => true,
        'count' => count($data),
        'download_url' => '/store-admin/exports/' . $filename . '.csv',
        'filename' => $filename . '.csv'
    ];
}

/**
 * Generate Excel export (basic CSV format for now)
 */
function generateExcelExport($data, $exportDir, $filename): array
{
    // For a basic implementation, we'll use CSV format
    // In a full implementation, you might use PhpSpreadsheet library
    return generateCSVExport($data, $exportDir, $filename);
}

/**
 * Generate JSON export
 */
function generateJSONExport($data, $exportDir, $filename): array
{
    $filepath = $exportDir . $filename . '.json';
    $jsonData = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

    if (file_put_contents($filepath, $jsonData) === false) {
        return ['success' => false, 'error' => 'Could not create export file'];
    }

    return [
        'success' => true,
        'count' => count($data),
        'download_url' => '/store-admin/exports/' . $filename . '.json',
        'filename' => $filename . '.json'
    ];
}

/**
 * Send SMS via Twilio
 */
function sendSMSViaTwilio($to, $message): bool
{
    try {
        error_log("sendSMSViaTwilio called - To: $to, Message: $message");

        $accountSid = Application::getSetting('twilio_account_sid', '');
        $authToken = Application::getSetting('twilio_auth_token', '');
        $fromNumber = Application::getSetting('twilio_from_number', '');

        error_log("Twilio config - SID: " . substr($accountSid, 0, 10) . "..., Token: " . (strlen($authToken) > 0 ? 'SET' : 'EMPTY') . ", From: $fromNumber");

        // File logging for debugging
        file_put_contents(
            __DIR__ . '/../sms_debug.log',
            date('Y-m-d H:i:s') . " - sendSMSViaTwilio called - To: $to, Message: $message\n" .
                date('Y-m-d H:i:s') . " - Twilio config - SID: " . substr($accountSid, 0, 10) . "..., Token: " . (strlen($authToken) > 0 ? 'SET' : 'EMPTY') . ", From: $fromNumber\n",
            FILE_APPEND | LOCK_EX
        );

        if (empty($accountSid) || empty($authToken) || empty($fromNumber)) {
            error_log("Twilio configuration incomplete - SID empty: " . (empty($accountSid) ? 'YES' : 'NO') . ", Token empty: " . (empty($authToken) ? 'YES' : 'NO') . ", From empty: " . (empty($fromNumber) ? 'YES' : 'NO'));
            return false;
        }

        $url = "https://api.twilio.com/2010-04-01/Accounts/$accountSid/Messages.json";

        $data = [
            'From' => $fromNumber,
            'To' => $to,
            'Body' => $message
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_USERPWD, "$accountSid:$authToken");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        // Check for cURL errors first
        if ($response === false || !empty($curlError)) {
            error_log("Twilio SMS cURL error: $curlError, HTTP Code: $httpCode");
            return false;
        }

        error_log("Twilio API response - HTTP Code: $httpCode, Response: $response");

        // File logging for API response
        file_put_contents(
            __DIR__ . '/../sms_debug.log',
            date('Y-m-d H:i:s') . " - Twilio API response - HTTP Code: $httpCode, Response: $response\n",
            FILE_APPEND | LOCK_EX
        );

        if ($httpCode >= 200 && $httpCode < 300) {
            $responseData = json_decode($response, true);
            if (isset($responseData['sid'])) {
                error_log("Twilio SMS sent successfully. SID: " . $responseData['sid']);
                return true;
            } else {
                error_log("Twilio SMS - Success HTTP code but no SID in response");
                return false;
            }
        }

        error_log("Twilio SMS failed. HTTP Code: $httpCode, Response: $response");
        return false;
    } catch (Exception $e) {
        error_log("Twilio SMS error: " . $e->getMessage());
        return false;
    }
}

/**
 * Send SMS via Nexmo (Vonage)
 */
function sendSMSViaNexmo($to, $message): bool
{
    try {
        file_put_contents(
            __DIR__ . '/../sms_debug.log',
            date('Y-m-d H:i:s') . " - sendSMSViaNexmo called - To: $to, Message: $message\n",
            FILE_APPEND | LOCK_EX
        );

        $apiKey = Application::getSetting('nexmo_api_key', '');
        $apiSecret = Application::getSetting('nexmo_api_secret', '');
        $fromNumber = Application::getSetting('nexmo_from_number', '');

        file_put_contents(
            __DIR__ . '/../sms_debug.log',
            date('Y-m-d H:i:s') . " - Nexmo config - API Key: " . (empty($apiKey) ? 'EMPTY' : 'SET') .
                ", API Secret: " . (empty($apiSecret) ? 'EMPTY' : 'SET') .
                ", From Number: $fromNumber\n",
            FILE_APPEND | LOCK_EX
        );

        if (empty($apiKey) || empty($apiSecret) || empty($fromNumber)) {
            error_log("Nexmo configuration incomplete");
            file_put_contents(
                __DIR__ . '/../sms_debug.log',
                date('Y-m-d H:i:s') . " - Nexmo configuration incomplete\n",
                FILE_APPEND | LOCK_EX
            );
            return false;
        }

        $url = "https://rest.nexmo.com/sms/json";

        $data = [
            'api_key' => $apiKey,
            'api_secret' => $apiSecret,
            'from' => $fromNumber,
            'to' => $to,
            'text' => $message
        ];

        file_put_contents(
            __DIR__ . '/../sms_debug.log',
            date('Y-m-d H:i:s') . " - Nexmo API call - URL: $url, From: $fromNumber, To: $to, Text: $message\n",
            FILE_APPEND | LOCK_EX
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);

        file_put_contents(
            __DIR__ . '/../sms_debug.log',
            date('Y-m-d H:i:s') . " - Nexmo cURL executing...\n",
            FILE_APPEND | LOCK_EX
        );

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        file_put_contents(
            __DIR__ . '/../sms_debug.log',
            date('Y-m-d H:i:s') . " - Nexmo API response - HTTP Code: $httpCode, cURL Error: $curlError, Full Response: $response\n",
            FILE_APPEND | LOCK_EX
        );

        if ($httpCode >= 200 && $httpCode < 300) {
            $responseData = json_decode($response, true);

            file_put_contents(
                __DIR__ . '/../sms_debug.log',
                date('Y-m-d H:i:s') . " - Nexmo response parsed - JSON: " . json_encode($responseData, JSON_PRETTY_PRINT) . "\n",
                FILE_APPEND | LOCK_EX
            );

            if (isset($responseData['messages'][0])) {
                $message = $responseData['messages'][0];
                $status = $message['status'] ?? 'unknown';
                $messageId = $message['message-id'] ?? 'unknown';
                $errorText = $message['error-text'] ?? 'none';

                file_put_contents(
                    __DIR__ . '/../sms_debug.log',
                    date('Y-m-d H:i:s') . " - Nexmo message details - Status: $status, Message ID: $messageId, Error Text: $errorText\n",
                    FILE_APPEND | LOCK_EX
                );

                if ($status == '0') {
                    error_log("Nexmo SMS sent successfully. Message ID: $messageId");
                    file_put_contents(
                        __DIR__ . '/../sms_debug.log',
                        date('Y-m-d H:i:s') . " - Nexmo SMS sent successfully\n",
                        FILE_APPEND | LOCK_EX
                    );
                    return true;
                } else {
                    file_put_contents(
                        __DIR__ . '/../sms_debug.log',
                        date('Y-m-d H:i:s') . " - Nexmo SMS failed with status: $status, error: $errorText\n",
                        FILE_APPEND | LOCK_EX
                    );
                }
            }
        }

        error_log("Nexmo SMS failed. HTTP Code: $httpCode, Response: $response");
        file_put_contents(
            __DIR__ . '/../sms_debug.log',
            date('Y-m-d H:i:s') . " - Nexmo SMS failed. HTTP Code: $httpCode, Response: $response\n",
            FILE_APPEND | LOCK_EX
        );
        return false;
    } catch (Exception $e) {
        error_log("Nexmo SMS error: " . $e->getMessage());
        return false;
    }
}

/**
 * Send SMS via Apifon (Greek SMS Provider)
 */
function sendSMSViaApifon($to, $message): bool
{
    try {
        file_put_contents(
            __DIR__ . '/../sms_debug.log',
            date('Y-m-d H:i:s') . " - sendSMSViaApifon called - To: $to, Message: $message\n",
            FILE_APPEND | LOCK_EX
        );

        $apiKey = Application::getSetting('apifon_api_key', '');
        $apiToken = Application::getSetting('apifon_api_token', '');
        $clientId = Application::getSetting('apifon_client_id', '');
        $username = Application::getSetting('apifon_username', '');
        $sender = Application::getSetting('apifon_sender', '');

        file_put_contents(
            __DIR__ . '/../sms_debug.log',
            date('Y-m-d H:i:s') . " - Apifon config - API Key: " . (empty($apiKey) ? 'EMPTY' : 'SET') .
                ", API Token: " . (empty($apiToken) ? 'EMPTY' : 'SET') .
                ", Client ID: " . (empty($clientId) ? 'EMPTY' : 'SET') .
                ", Username: " . (empty($username) ? 'EMPTY' : 'SET') .
                ", Sender: $sender\n",
            FILE_APPEND | LOCK_EX
        );

        // Determine which authentication method to use
        $authToken = '';
        if (!empty($apiToken)) {
            $authToken = $apiToken;
            file_put_contents(
                __DIR__ . '/../sms_debug.log',
                date('Y-m-d H:i:s') . " - Using API Token for authentication\n",
                FILE_APPEND | LOCK_EX
            );
        } elseif (!empty($apiKey)) {
            $authToken = $apiKey;
            file_put_contents(
                __DIR__ . '/../sms_debug.log',
                date('Y-m-d H:i:s') . " - Using API Key for authentication\n",
                FILE_APPEND | LOCK_EX
            );
        } else {
            error_log("Apifon configuration incomplete - No API Key or Token");
            file_put_contents(
                __DIR__ . '/../sms_debug.log',
                date('Y-m-d H:i:s') . " - Apifon configuration incomplete - No API Key or Token\n",
                FILE_APPEND | LOCK_EX
            );
            return false;
        }

        // Apifon API endpoint (from official documentation)
        $url = "https://ars.apifon.com/services/api/v1/sms/send";

        // Remove + from phone number for Greek providers
        $cleanPhone = ltrim($to, '+');

        // Apifon requires specific JSON structure (from documentation)
        $data = [
            'subscribers' => [
                [
                    'number' => $cleanPhone
                ]
            ],
            'message' => [
                'text' => $message,
                'sender_id' => $sender ?: 'SMS'
            ]
        ];

        file_put_contents(
            __DIR__ . '/../sms_debug.log',
            date('Y-m-d H:i:s') . " - Apifon API call - URL: $url, CleanPhone: $cleanPhone, Sender: " . ($sender ?: 'SMS') . "\n",
            FILE_APPEND | LOCK_EX
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        // Try different authentication methods based on available credentials
        $date = gmdate('D, d M Y H:i:s T');

        if (!empty($clientId) && !empty($apiToken)) {
            // Method 1: Client ID + API Token (common for newer APIs)
            file_put_contents(
                __DIR__ . '/../sms_debug.log',
                date('Y-m-d H:i:s') . " - Using Client ID + API Token authentication\n",
                FILE_APPEND | LOCK_EX
            );

            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'X-ApifonWS-Date: ' . $date,
                'X-Client-ID: ' . $clientId,
                'Authorization: Bearer ' . $apiToken
            ]);
        } elseif (!empty($username) && !empty($apiToken)) {
            // Method 2: Username + API Token (basic auth style)
            file_put_contents(
                __DIR__ . '/../sms_debug.log',
                date('Y-m-d H:i:s') . " - Using Username + API Token authentication\n",
                FILE_APPEND | LOCK_EX
            );

            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'X-ApifonWS-Date: ' . $date,
                'Authorization: Basic ' . base64_encode($username . ':' . $apiToken)
            ]);
        } else {
            // Method 3: Simple Bearer token (fallback)
            file_put_contents(
                __DIR__ . '/../sms_debug.log',
                date('Y-m-d H:i:s') . " - Using simple Bearer token authentication\n",
                FILE_APPEND | LOCK_EX
            );

            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'X-ApifonWS-Date: ' . $date,
                'Authorization: Bearer ' . $authToken
            ]);
        }

        file_put_contents(
            __DIR__ . '/../sms_debug.log',
            date('Y-m-d H:i:s') . " - Apifon cURL executing...\n",
            FILE_APPEND | LOCK_EX
        );

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        file_put_contents(
            __DIR__ . '/../sms_debug.log',
            date('Y-m-d H:i:s') . " - Apifon API response - HTTP Code: $httpCode, cURL Error: $curlError, Response: $response\n",
            FILE_APPEND | LOCK_EX
        );

        if ($httpCode >= 200 && $httpCode < 300) {
            $responseData = json_decode($response, true);
            file_put_contents(
                __DIR__ . '/../sms_debug.log',
                date('Y-m-d H:i:s') . " - Apifon response parsed - Status Code: " . ($responseData['result_info']['status_code'] ?? 'not_set') . "\n",
                FILE_APPEND | LOCK_EX
            );
            // Apifon success is determined by result_info.status_code = 200
            if (isset($responseData['result_info']['status_code']) && $responseData['result_info']['status_code'] == 200) {
                error_log("Apifon SMS sent successfully");
                file_put_contents(
                    __DIR__ . '/../sms_debug.log',
                    date('Y-m-d H:i:s') . " - Apifon SMS sent successfully\n",
                    FILE_APPEND | LOCK_EX
                );
                return true;
            }
        }

        error_log("Apifon SMS failed. HTTP Code: $httpCode, Response: $response");
        file_put_contents(
            __DIR__ . '/../sms_debug.log',
            date('Y-m-d H:i:s') . " - Apifon SMS failed. HTTP Code: $httpCode, Response: $response\n",
            FILE_APPEND | LOCK_EX
        );
        return false;
    } catch (Exception $e) {
        error_log("Apifon SMS error: " . $e->getMessage());
        return false;
    }
}

/**
 * Send SMS via SMS-Tool (Greek SMS Provider)
 */
function sendSMSViaSMStools($to, $message): bool
{
    try {
        $username = Application::getSetting('smstools_username', '');
        $password = Application::getSetting('smstools_password', '');
        $sender = Application::getSetting('smstools_sender', '');

        if (empty($username) || empty($password)) {
            error_log("SMS-Tool configuration incomplete");
            return false;
        }

        // SMS-Tool API endpoint (common pattern for Greek SMS providers)
        $url = "https://www.sms-tool.gr/api/send";

        // Remove + from phone number for Greek providers
        $cleanPhone = ltrim($to, '+');

        // SMS-Tool API parameters (typical format)
        $data = [
            'username' => $username,
            'password' => $password,
            'recipient' => $cleanPhone,
            'message' => $message,
            'sender' => $sender ?: 'SMS',
            'type' => 'text'
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        if ($curlError) {
            error_log("SMS-Tool cURL error: " . $curlError);
            return false;
        }

        if ($httpCode >= 200 && $httpCode < 300) {
            // SMS-Tool typically returns simple response
            $response = trim($response);
            if (
                strpos($response, 'OK') !== false ||
                strpos($response, 'success') !== false ||
                strpos($response, '200') !== false ||
                is_numeric($response)
            ) { // Message ID returned
                error_log("SMS-Tool SMS sent successfully. Response: $response");
                return true;
            }
        }

        error_log("SMS-Tool SMS failed. HTTP Code: $httpCode, Response: $response");
        return false;
    } catch (Exception $e) {
        error_log("SMS-Tool SMS error: " . $e->getMessage());
        return false;
    }
}

/**
 * Send SMS via SMS.to (Global SMS Provider)
 */
function sendSMSViaSMSto($to, $message): bool
{
    try {
        file_put_contents(
            __DIR__ . '/../sms_debug.log',
            date('Y-m-d H:i:s') . " - sendSMSViaSMSto called - To: $to, Message: $message\n",
            FILE_APPEND | LOCK_EX
        );

        $apiKey = Application::getSetting('smsto_api_key', '');
        $sender = Application::getSetting('smsto_sender', '');

        file_put_contents(
            __DIR__ . '/../sms_debug.log',
            date('Y-m-d H:i:s') . " - SMS.to config - API Key: " . (empty($apiKey) ? 'EMPTY' : 'SET') .
                ", Sender: $sender\n",
            FILE_APPEND | LOCK_EX
        );

        if (empty($apiKey)) {
            error_log("SMS.to configuration incomplete - API Key is empty");
            file_put_contents(
                __DIR__ . '/../sms_debug.log',
                date('Y-m-d H:i:s') . " - SMS.to configuration incomplete - API Key is empty\n",
                FILE_APPEND | LOCK_EX
            );
            return false;
        }

        // SMS.to API endpoint
        $url = "https://api.sms.to/sms/send";

        // Clean phone number (keep + for international format)
        $cleanPhone = $to;

        // SMS.to API request format
        $data = [
            'message' => $message,
            'to' => $cleanPhone,
            'sender_id' => $sender ?: 'SMS'
        ];

        file_put_contents(
            __DIR__ . '/../sms_debug.log',
            date('Y-m-d H:i:s') . " - SMS.to API call - URL: $url, CleanPhone: $cleanPhone, Sender: " . ($sender ?: 'SMS') . "\n",
            FILE_APPEND | LOCK_EX
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $apiKey
        ]);

        file_put_contents(
            __DIR__ . '/../sms_debug.log',
            date('Y-m-d H:i:s') . " - SMS.to cURL executing...\n",
            FILE_APPEND | LOCK_EX
        );

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        file_put_contents(
            __DIR__ . '/../sms_debug.log',
            date('Y-m-d H:i:s') . " - SMS.to API response - HTTP Code: $httpCode, cURL Error: $curlError, Full Response: $response\n",
            FILE_APPEND | LOCK_EX
        );

        if ($httpCode >= 200 && $httpCode < 300) {
            $responseData = json_decode($response, true);

            file_put_contents(
                __DIR__ . '/../sms_debug.log',
                date('Y-m-d H:i:s') . " - SMS.to response parsed - JSON: " . json_encode($responseData, JSON_PRETTY_PRINT) . "\n",
                FILE_APPEND | LOCK_EX
            );

            // SMS.to success response format (common patterns)
            if (isset($responseData['success']) && $responseData['success']) {
                error_log("SMS.to SMS sent successfully");
                file_put_contents(
                    __DIR__ . '/../sms_debug.log',
                    date('Y-m-d H:i:s') . " - SMS.to SMS sent successfully\n",
                    FILE_APPEND | LOCK_EX
                );
                return true;
            } elseif (isset($responseData['status']) && $responseData['status'] === 'success') {
                error_log("SMS.to SMS sent successfully");
                file_put_contents(
                    __DIR__ . '/../sms_debug.log',
                    date('Y-m-d H:i:s') . " - SMS.to SMS sent successfully\n",
                    FILE_APPEND | LOCK_EX
                );
                return true;
            } else {
                $errorMsg = $responseData['message'] ?? $responseData['error'] ?? 'Unknown error';
                file_put_contents(
                    __DIR__ . '/../sms_debug.log',
                    date('Y-m-d H:i:s') . " - SMS.to SMS failed with error: $errorMsg\n",
                    FILE_APPEND | LOCK_EX
                );
            }
        }

        error_log("SMS.to SMS failed. HTTP Code: $httpCode, Response: $response");
        file_put_contents(
            __DIR__ . '/../sms_debug.log',
            date('Y-m-d H:i:s') . " - SMS.to SMS failed. HTTP Code: $httpCode, Response: $response\n",
            FILE_APPEND | LOCK_EX
        );
        return false;
    } catch (Exception $e) {
        error_log("SMS.to SMS error: " . $e->getMessage());
        file_put_contents(
            __DIR__ . '/../sms_debug.log',
            date('Y-m-d H:i:s') . " - SMS.to SMS error: " . $e->getMessage() . "\n",
            FILE_APPEND | LOCK_EX
        );
        return false;
    }
}

/**
 * Send SMS via Custom webhook/API
 */
function sendSMSViaCustom($to, $message): bool
{
    try {
        $webhookUrl = Application::getSetting('custom_sms_webhook', '');
        $apiKey = Application::getSetting('custom_sms_api_key', '');

        if (empty($webhookUrl)) {
            error_log("Custom SMS webhook URL not configured");
            return false;
        }

        $data = [
            'to' => $to,
            'message' => $message,
            'api_key' => $apiKey,
            'timestamp' => time()
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $webhookUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'User-Agent: Store-Admin-SMS/1.0'
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode >= 200 && $httpCode < 300) {
            error_log("Custom SMS webhook called successfully");
            return true;
        }

        error_log("Custom SMS webhook failed. HTTP Code: $httpCode, Response: $response");
        return false;
    } catch (Exception $e) {
        error_log("Custom SMS error: " . $e->getMessage());
        return false;
    }
}

/**
 * Debug bulk tables - check if they exist
 */
function debugBulkTables(Database $db): array
{
    $result = [
        'tenant' => TenantManager::getCurrentTenant(),
        'database_path' => $db->getDatabasePath(),
        'tables' => []
    ];

    $tables = ['email_log', 'sms_log', 'employee_notifications', 'reminder_log'];

    foreach ($tables as $table) {
        try {
            $tableCheck = $db->fetchRow("SELECT name FROM sqlite_master WHERE type='table' AND name=:table", [':table' => $table]);
            $result['tables'][$table] = $tableCheck ? 'EXISTS' : 'MISSING';
        } catch (Exception $e) {
            $result['tables'][$table] = 'ERROR: ' . $e->getMessage();
        }
    }

    return ['success' => true, 'debug' => $result];
}

/**
 * Get email logs with pagination and filtering
 */
function getEmailLogs(array $data, Database $db): array
{
    try {
        $page = max(1, intval($data['page'] ?? 1));
        $limit = max(1, min(100, intval($data['limit'] ?? 20)));
        $offset = ($page - 1) * $limit;

        $customerId = $data['customer_id'] ?? '';
        $dateFrom = $data['date_from'] ?? '';
        $dateTo = $data['date_to'] ?? '';
        $search = $data['search'] ?? '';

        // Build WHERE clause
        $whereConditions = [];
        $params = [];

        if ($customerId) {
            $whereConditions[] = "el.customer_id = :customer_id";
            $params[':customer_id'] = $customerId;
        }

        if ($dateFrom) {
            $whereConditions[] = "DATE(el.sent_at) >= :date_from";
            $params[':date_from'] = $dateFrom;
        }

        if ($dateTo) {
            $whereConditions[] = "DATE(el.sent_at) <= :date_to";
            $params[':date_to'] = $dateTo;
        }

        if ($search) {
            $whereConditions[] = "(el.subject LIKE :search OR el.content LIKE :search OR c.name LIKE :search OR c.email LIKE :search)";
            $params[':search'] = '%' . $search . '%';
        }

        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

        // Get total count
        $countQuery = "
            SELECT COUNT(*) as total
            FROM email_logs el
            LEFT JOIN customers c ON el.customer_id = c.id
            $whereClause
        ";
        $totalResult = $db->fetchRow($countQuery, $params);
        $total = $totalResult['total'] ?? 0;

        // Get logs with customer info
        $query = "
            SELECT
                el.id,
                el.customer_id,
                el.subject,
                el.content as message,
                el.sent_at,
                el.created_at,
                c.name as customer_name,
                c.email as customer_email
            FROM email_logs el
            LEFT JOIN customers c ON el.customer_id = c.id
            $whereClause
            ORDER BY el.sent_at DESC
            LIMIT :limit OFFSET :offset
        ";

        $params[':limit'] = $limit;
        $params[':offset'] = $offset;

        $logs = $db->fetchAll($query, $params);

        return [
            'success' => true,
            'logs' => $logs,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ];
    } catch (Exception $e) {
        return ['success' => false, 'error' => 'Error fetching email logs: ' . $e->getMessage()];
    }
}

/**
 * Get SMS logs with pagination and filtering
 */
function getSMSLogs(array $data, Database $db): array
{
    try {
        $page = max(1, intval($data['page'] ?? 1));
        $limit = max(1, min(100, intval($data['limit'] ?? 20)));
        $offset = ($page - 1) * $limit;

        $customerId = $data['customer_id'] ?? '';
        $dateFrom = $data['date_from'] ?? '';
        $dateTo = $data['date_to'] ?? '';
        $search = $data['search'] ?? '';

        // Build WHERE clause
        $whereConditions = [];
        $params = [];

        if ($customerId) {
            $whereConditions[] = "sl.customer_id = :customer_id";
            $params[':customer_id'] = $customerId;
        }

        if ($dateFrom) {
            $whereConditions[] = "DATE(sl.sent_at) >= :date_from";
            $params[':date_from'] = $dateFrom;
        }

        if ($dateTo) {
            $whereConditions[] = "DATE(sl.sent_at) <= :date_to";
            $params[':date_to'] = $dateTo;
        }

        if ($search) {
            $whereConditions[] = "(sl.message LIKE :search OR c.name LIKE :search OR c.phone LIKE :search)";
            $params[':search'] = '%' . $search . '%';
        }

        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

        // Get total count
        $countQuery = "
            SELECT COUNT(*) as total
            FROM sms_log sl
            LEFT JOIN customers c ON sl.customer_id = c.id
            $whereClause
        ";
        $totalResult = $db->fetchRow($countQuery, $params);
        $total = $totalResult['total'] ?? 0;

        // Get logs with customer info
        $query = "
            SELECT
                sl.id,
                sl.customer_id,
                sl.message,
                sl.sent_at,
                sl.created_at,
                c.name as customer_name,
                c.phone as customer_phone
            FROM sms_log sl
            LEFT JOIN customers c ON sl.customer_id = c.id
            $whereClause
            ORDER BY sl.sent_at DESC
            LIMIT :limit OFFSET :offset
        ";

        $params[':limit'] = $limit;
        $params[':offset'] = $offset;

        $logs = $db->fetchAll($query, $params);

        return [
            'success' => true,
            'logs' => $logs,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ];
    } catch (Exception $e) {
        return ['success' => false, 'error' => 'Error fetching SMS logs: ' . $e->getMessage()];
    }
}

/**
 * Save translations
 */
function saveTranslations(array $data, Database $db): array
{
    try {
        require_once __DIR__ . '/TranslationController.php';

        // Map the AJAX action to the controller action
        $data['action'] = 'save';

        return TranslationController::handleRequest($data, $db);
    } catch (Exception $e) {
        return ['success' => false, 'error' => 'Error saving translations: ' . $e->getMessage()];
    }
}

/**
 * Get a specific email log by ID
 */
function getEmailLog(array $data, Database $db): array
{
    $logId = $data['log_id'] ?? '';

    if (!$logId) {
        return ['success' => false, 'error' => 'Missing log ID'];
    }

    try {
        require_once __DIR__ . '/../../shared/EmailLogger.php';

        $logger = new EmailLogger($db);
        $email = $logger->getEmailLog($logId);

        if (!$email) {
            return ['success' => false, 'error' => 'Email log not found'];
        }

        return ['success' => true, 'email' => $email];
    } catch (Exception $e) {
        return ['success' => false, 'error' => 'Error fetching email log: ' . $e->getMessage()];
    }
}

/**
 * Send custom email to customer
 */
function sendCustomEmail(array $data, Database $db): array
{
    $customerId = $data['customer_id'] ?? '';
    $subject = $data['subject'] ?? '';
    $content = $data['content'] ?? '';

    if (!$customerId || !$subject || !$content) {
        return ['success' => false, 'error' => 'Missing required fields'];
    }

    try {
        // Get customer details
        $customer = $db->fetchRow(
            "SELECT * FROM customers WHERE id = :id",
            [':id' => $customerId]
        );

        if (!$customer) {
            return ['success' => false, 'error' => 'Customer not found'];
        }

        if (!$customer['email']) {
            return ['success' => false, 'error' => 'Customer has no email address'];
        }

        // Send email using SMTP class
        require_once __DIR__ . '/../../shared/smtp.php';

        $mailer = new SMTPMailer();
        $success = $mailer->sendCustomEmail(
            $customer['email'],
            $subject,
            $content,
            $customer['name'],
            $customer['id']
        );

        if ($success) {
            return ['success' => true, 'message' => 'Email sent successfully'];
        } else {
            return ['success' => false, 'error' => 'Failed to send email'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'error' => 'Error sending email: ' . $e->getMessage()];
    }
}

/**
 * Get categories data for XLSX export
 */
function getCategoriesData($db, $params)
{
    try {
        $search = $params['search'] ?? '';
        $sort = $params['sort'] ?? '';
        $status = $params['status'] ?? '';

        $sql = "SELECT c.id, c.name, c.description, c.icon, c.color, c.is_active, c.sort_order,
                       c.created_at, c.updated_at, COUNT(s.id) as services_count
                FROM categories c
                LEFT JOIN services s ON c.id = s.category_id AND s.is_active = 1
                WHERE 1=1";
        $sqlParams = [];

        if ($search) {
            $sql .= " AND (c.name LIKE ? OR c.description LIKE ?)";
            $searchTerm = "%$search%";
            $sqlParams[] = $searchTerm;
            $sqlParams[] = $searchTerm;
        }

        if ($status) {
            if ($status === 'active') {
                $sql .= " AND c.is_active = 1";
            } elseif ($status === 'inactive') {
                $sql .= " AND c.is_active = 0";
            }
        }

        $sql .= " GROUP BY c.id";

        // Add sorting
        switch ($sort) {
            case 'name':
                $sql .= " ORDER BY c.name ASC";
                break;
            case 'services_count':
                $sql .= " ORDER BY services_count DESC";
                break;
            case 'created_at':
                $sql .= " ORDER BY c.created_at DESC";
                break;
            default:
                $sql .= " ORDER BY c.sort_order ASC, c.name ASC";
        }

        $categories = $db->fetchAll($sql, $sqlParams);
        return ['success' => true, 'data' => $categories];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Get translations data for XLSX export
 */
function getTranslationsData($db, $params)
{
    try {
        $search = $params['search'] ?? '';
        $category = $params['category'] ?? '';

        $sql = "SELECT key, category, value_el, value_en, created_at, updated_at
                FROM translations
                WHERE 1=1";
        $sqlParams = [];

        // Only export client-facing translations
        $clientCategories = ['general', 'booking_system', 'booking_flow', 'user_interface', 'services', 'categories', 'client', 'ui', 'dates'];
        $sql .= " AND category IN ('" . implode("','", $clientCategories) . "')";

        if ($search) {
            $sql .= " AND (key LIKE ? OR value_el LIKE ? OR value_en LIKE ? OR category LIKE ?)";
            $searchTerm = "%$search%";
            $sqlParams[] = $searchTerm;
            $sqlParams[] = $searchTerm;
            $sqlParams[] = $searchTerm;
            $sqlParams[] = $searchTerm;
        }

        if ($category && $category !== 'all' && in_array($category, $clientCategories)) {
            $sql .= " AND category = ?";
            $sqlParams[] = $category;
        }

        $sql .= " ORDER BY category ASC, key ASC";

        $translations = $db->fetchAll($sql, $sqlParams);
        return ['success' => true, 'data' => $translations];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Import categories from XLSX data
 */
function importCategories($db, $params)
{
    try {
        $data = json_decode($params['data'], true);
        if (!$data) {
            return ['success' => false, 'error' => 'Invalid data format'];
        }

        $imported = 0;
        $errors = [];

        foreach ($data as $row) {
            try {
                // Validate required fields
                if (empty($row['name'])) {
                    $errors[] = "Row skipped: name is required";
                    continue;
                }

                // Generate ID if not provided
                $id = $row['id'] ?? uniqid('cat_');

                // Set defaults
                $description = $row['description'] ?? '';
                $icon = $row['icon'] ?? 'fas fa-tag';
                $color = $row['color'] ?? 'blue';
                $isActive = isset($row['is_active']) ? (int)$row['is_active'] : 1;
                $sortOrder = isset($row['sort_order']) ? (int)$row['sort_order'] : 999;

                // Check if category already exists
                $existing = $db->fetchRow(
                    "SELECT id FROM categories WHERE name = ?",
                    [$row['name']]
                );

                if ($existing) {
                    // Update existing category
                    $result = $db->query(
                        "UPDATE categories SET description = ?, icon = ?, color = ?, is_active = ?, sort_order = ?, updated_at = ? WHERE name = ?",
                        [$description, $icon, $color, $isActive, $sortOrder, date('Y-m-d H:i:s'), $row['name']]
                    );
                } else {
                    // Insert new category
                    $result = $db->query(
                        "INSERT INTO categories (id, name, description, icon, color, is_active, sort_order, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
                        [$id, $row['name'], $description, $icon, $color, $isActive, $sortOrder, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]
                    );
                }

                if ($result) {
                    $imported++;
                }
            } catch (Exception $e) {
                $errors[] = "Error importing '{$row['name']}': " . $e->getMessage();
            }
        }

        return [
            'success' => true,
            'imported' => $imported,
            'errors' => $errors
        ];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Import translations from XLSX data
 */
function importTranslations($db, $params)
{
    try {
        $data = json_decode($params['data'], true);
        if (!$data) {
            return ['success' => false, 'error' => 'Invalid data format'];
        }

        $imported = 0;
        $errors = [];
        $clientCategories = ['general', 'booking_system', 'booking_flow', 'user_interface', 'services', 'categories', 'client', 'ui', 'dates'];

        foreach ($data as $row) {
            try {
                // Validate required fields
                if (empty($row['key']) || empty($row['category'])) {
                    $errors[] = "Row skipped: key and category are required";
                    continue;
                }

                // Only allow client categories
                if (!in_array($row['category'], $clientCategories)) {
                    $errors[] = "Row skipped: category '{$row['category']}' is not allowed";
                    continue;
                }

                $key = $row['key'];
                $category = $row['category'];
                $valueEl = $row['value_el'] ?? '';
                $valueEn = $row['value_en'] ?? '';

                // Check if translation already exists
                $existing = $db->fetchRow(
                    "SELECT key FROM translations WHERE key = ? AND category = ?",
                    [$key, $category]
                );

                if ($existing) {
                    // Update existing translation
                    $result = $db->query(
                        "UPDATE translations SET value_el = ?, value_en = ?, updated_at = ? WHERE key = ? AND category = ?",
                        [$valueEl, $valueEn, date('Y-m-d H:i:s'), $key, $category]
                    );
                } else {
                    // Insert new translation
                    $result = $db->query(
                        "INSERT INTO translations (key, category, value_el, value_en, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)",
                        [$key, $category, $valueEl, $valueEn, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]
                    );
                }

                if ($result) {
                    $imported++;
                }
            } catch (Exception $e) {
                $errors[] = "Error importing '{$row['key']}': " . $e->getMessage();
            }
        }

        return [
            'success' => true,
            'imported' => $imported,
            'errors' => $errors
        ];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Get customers data for XLSX export
 */
function getCustomersData($db, $params)
{
    try {
        $search = $params['search'] ?? '';
        $status = $params['status'] ?? '';
        $dateFrom = $params['date_from'] ?? '';
        $dateTo = $params['date_to'] ?? '';

        $sql = "SELECT id, name, email, phone, language, notes, created_at, updated_at
                FROM customers
                WHERE 1=1";
        $sqlParams = [];

        if ($search) {
            $sql .= " AND (name LIKE ? OR email LIKE ? OR phone LIKE ?)";
            $searchTerm = "%$search%";
            $sqlParams[] = $searchTerm;
            $sqlParams[] = $searchTerm;
            $sqlParams[] = $searchTerm;
        }

        if ($dateFrom) {
            $sql .= " AND created_at >= ?";
            $sqlParams[] = $dateFrom . ' 00:00:00';
        }

        if ($dateTo) {
            $sql .= " AND created_at <= ?";
            $sqlParams[] = $dateTo . ' 23:59:59';
        }

        $sql .= " ORDER BY created_at DESC";

        $customers = $db->fetchAll($sql, $sqlParams);
        return ['success' => true, 'data' => $customers];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Import customers from XLSX data
 */
function importCustomers($db, $params)
{
    try {
        $data = json_decode($params['data'], true);
        if (!$data) {
            return ['success' => false, 'error' => 'Invalid data format'];
        }

        $imported = 0;
        $errors = [];

        foreach ($data as $row) {
            try {
                // Validate required fields
                if (empty($row['name']) || empty($row['email'])) {
                    $errors[] = "Row skipped: name and email are required";
                    continue;
                }

                // Generate ID if not provided
                $id = $row['id'] ?? uniqid('cust_');

                // Set defaults
                $phone = $row['phone'] ?? '';
                $language = $row['language'] ?? 'en';
                $notes = $row['notes'] ?? '';

                // Check if customer already exists
                $existing = $db->fetchRow(
                    "SELECT id FROM customers WHERE email = ?",
                    [$row['email']]
                );

                if ($existing) {
                    // Update existing customer
                    $result = $db->query(
                        "UPDATE customers SET name = ?, phone = ?, language = ?, notes = ?, updated_at = ? WHERE email = ?",
                        [$row['name'], $phone, $language, $notes, date('Y-m-d H:i:s'), $row['email']]
                    );
                } else {
                    // Insert new customer
                    $result = $db->query(
                        "INSERT INTO customers (id, name, email, phone, language, notes, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                        [$id, $row['name'], $row['email'], $phone, $language, $notes, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]
                    );
                }

                if ($result) {
                    $imported++;
                }
            } catch (Exception $e) {
                $errors[] = "Error importing '{$row['name']}': " . $e->getMessage();
            }
        }

        return [
            'success' => true,
            'imported' => $imported,
            'errors' => $errors
        ];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Get services data for XLSX export
 */
function getServicesData($db, $params)
{
    try {
        $search = $params['search'] ?? '';
        $categoryId = $params['category_id'] ?? '';
        $priceMin = $params['price_min'] ?? '';
        $priceMax = $params['price_max'] ?? '';

        $sql = "SELECT s.id, s.name, s.description, s.price, s.duration, s.preparation_time, s.cleanup_time,
                       s.is_active, s.created_at, s.updated_at, c.name as category_name
                FROM services s
                LEFT JOIN categories c ON s.category_id = c.id
                WHERE 1=1";
        $sqlParams = [];

        if ($search) {
            $sql .= " AND (s.name LIKE ? OR s.description LIKE ?)";
            $searchTerm = "%$search%";
            $sqlParams[] = $searchTerm;
            $sqlParams[] = $searchTerm;
        }

        if ($categoryId) {
            $sql .= " AND s.category_id = ?";
            $sqlParams[] = $categoryId;
        }

        if ($priceMin) {
            $sql .= " AND s.price >= ?";
            $sqlParams[] = $priceMin;
        }

        if ($priceMax) {
            $sql .= " AND s.price <= ?";
            $sqlParams[] = $priceMax;
        }

        $sql .= " ORDER BY s.name ASC";

        $services = $db->fetchAll($sql, $sqlParams);
        return ['success' => true, 'data' => $services];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Import services from XLSX data
 */
function importServices($db, $params)
{
    try {
        $data = json_decode($params['data'], true);
        if (!$data) {
            return ['success' => false, 'error' => 'Invalid data format'];
        }

        $imported = 0;
        $errors = [];

        foreach ($data as $row) {
            try {
                // Validate required fields
                if (empty($row['name']) || empty($row['price']) || empty($row['duration'])) {
                    $errors[] = "Row skipped: name, price, and duration are required";
                    continue;
                }

                // Generate ID if not provided
                $id = $row['id'] ?? uniqid('serv_');

                // Set defaults
                $description = $row['description'] ?? '';
                $preparationTime = isset($row['preparation_time']) ? (int)$row['preparation_time'] : 0;
                $cleanupTime = isset($row['cleanup_time']) ? (int)$row['cleanup_time'] : 0;
                $isActive = isset($row['is_active']) ? (int)$row['is_active'] : 1;

                // Handle category
                $categoryId = null;
                if (!empty($row['category_name'])) {
                    $category = $db->fetchRow("SELECT id FROM categories WHERE name = ?", [$row['category_name']]);
                    if ($category) {
                        $categoryId = $category['id'];
                    }
                }

                // Check if service already exists
                $existing = $db->fetchRow(
                    "SELECT id FROM services WHERE name = ?",
                    [$row['name']]
                );

                if ($existing) {
                    // Update existing service
                    $result = $db->query(
                        "UPDATE services SET description = ?, price = ?, duration = ?, preparation_time = ?, cleanup_time = ?, category_id = ?, is_active = ?, updated_at = ? WHERE name = ?",
                        [$description, $row['price'], $row['duration'], $preparationTime, $cleanupTime, $categoryId, $isActive, date('Y-m-d H:i:s'), $row['name']]
                    );
                } else {
                    // Insert new service
                    $result = $db->query(
                        "INSERT INTO services (id, name, description, price, duration, preparation_time, cleanup_time, category_id, is_active, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                        [$id, $row['name'], $description, $row['price'], $row['duration'], $preparationTime, $cleanupTime, $categoryId, $isActive, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]
                    );
                }

                if ($result) {
                    $imported++;
                }
            } catch (Exception $e) {
                $errors[] = "Error importing '{$row['name']}': " . $e->getMessage();
            }
        }

        return [
            'success' => true,
            'imported' => $imported,
            'errors' => $errors
        ];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Get employees data for XLSX export
 */
function getEmployeesData($db, $params)
{
    try {
        $search = $params['search'] ?? '';
        $status = $params['status'] ?? '';
        $position = $params['position'] ?? '';

        $sql = "SELECT id, name, email, phone, position, is_active, created_at, updated_at
                FROM employees
                WHERE 1=1";
        $sqlParams = [];

        if ($search) {
            $sql .= " AND (name LIKE ? OR email LIKE ? OR phone LIKE ? OR position LIKE ?)";
            $searchTerm = "%$search%";
            $sqlParams[] = $searchTerm;
            $sqlParams[] = $searchTerm;
            $sqlParams[] = $searchTerm;
            $sqlParams[] = $searchTerm;
        }

        if ($status) {
            if ($status === 'active') {
                $sql .= " AND is_active = 1";
            } elseif ($status === 'inactive') {
                $sql .= " AND is_active = 0";
            }
        }

        if ($position) {
            $sql .= " AND position = ?";
            $sqlParams[] = $position;
        }

        $sql .= " ORDER BY name ASC";

        $employees = $db->fetchAll($sql, $sqlParams);
        return ['success' => true, 'data' => $employees];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Import employees from XLSX data
 */
function importEmployees($db, $params)
{
    try {
        $data = json_decode($params['data'], true);
        if (!$data) {
            return ['success' => false, 'error' => 'Invalid data format'];
        }

        $imported = 0;
        $errors = [];

        foreach ($data as $row) {
            try {
                // Validate required fields
                if (empty($row['name']) || empty($row['email'])) {
                    $errors[] = "Row skipped: name and email are required";
                    continue;
                }

                // Generate ID if not provided
                $id = $row['id'] ?? uniqid('emp_');

                // Set defaults
                $phone = $row['phone'] ?? '';
                $position = $row['position'] ?? 'Staff';
                $isActive = isset($row['is_active']) ? (int)$row['is_active'] : 1;

                // Check if employee already exists
                $existing = $db->fetchRow(
                    "SELECT id FROM employees WHERE email = ?",
                    [$row['email']]
                );

                if ($existing) {
                    // Update existing employee
                    $result = $db->query(
                        "UPDATE employees SET name = ?, phone = ?, position = ?, is_active = ?, updated_at = ? WHERE email = ?",
                        [$row['name'], $phone, $position, $isActive, date('Y-m-d H:i:s'), $row['email']]
                    );
                } else {
                    // Insert new employee
                    $result = $db->query(
                        "INSERT INTO employees (id, name, email, phone, position, is_active, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                        [$id, $row['name'], $row['email'], $phone, $position, $isActive, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]
                    );
                }

                if ($result) {
                    $imported++;
                }
            } catch (Exception $e) {
                $errors[] = "Error importing '{$row['name']}': " . $e->getMessage();
            }
        }

        return [
            'success' => true,
            'imported' => $imported,
            'errors' => $errors
        ];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Get reservations data for XLSX export
 */
function getReservationsData($db, $params)
{
    try {
        $search = $params['search'] ?? '';
        $status = $params['status'] ?? '';
        $dateFrom = $params['date_from'] ?? '';
        $dateTo = $params['date_to'] ?? '';
        $serviceId = $params['service_id'] ?? '';
        $employeeId = $params['employee_id'] ?? '';

        $sql = "SELECT r.id, r.customer_name, r.customer_email, r.customer_phone, r.date, r.start_time, r.end_time,
                       r.status, r.notes, r.created_at, r.updated_at, s.name as service_name, e.name as employee_name
                FROM reservations r
                LEFT JOIN services s ON r.service_id = s.id
                LEFT JOIN employees e ON r.employee_id = e.id
                WHERE 1=1";
        $sqlParams = [];

        if ($search) {
            $sql .= " AND (r.customer_name LIKE ? OR r.customer_email LIKE ? OR r.customer_phone LIKE ?)";
            $searchTerm = "%$search%";
            $sqlParams[] = $searchTerm;
            $sqlParams[] = $searchTerm;
            $sqlParams[] = $searchTerm;
        }

        if ($status) {
            $sql .= " AND r.status = ?";
            $sqlParams[] = $status;
        }

        if ($dateFrom) {
            $sql .= " AND r.date >= ?";
            $sqlParams[] = $dateFrom;
        }

        if ($dateTo) {
            $sql .= " AND r.date <= ?";
            $sqlParams[] = $dateTo;
        }

        if ($serviceId) {
            $sql .= " AND r.service_id = ?";
            $sqlParams[] = $serviceId;
        }

        if ($employeeId) {
            $sql .= " AND r.employee_id = ?";
            $sqlParams[] = $employeeId;
        }

        $sql .= " ORDER BY r.date DESC, r.start_time DESC";

        $reservations = $db->fetchAll($sql, $sqlParams);
        return ['success' => true, 'data' => $reservations];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Import reservations from XLSX data
 */
function importReservations($db, $params)
{
    try {
        $data = json_decode($params['data'], true);
        if (!$data) {
            return ['success' => false, 'error' => 'Invalid data format'];
        }

        $imported = 0;
        $errors = [];

        foreach ($data as $row) {
            try {
                // Validate required fields
                if (empty($row['customer_name']) || empty($row['customer_email']) || empty($row['date']) || empty($row['start_time'])) {
                    $errors[] = "Row skipped: customer_name, customer_email, date, and start_time are required";
                    continue;
                }

                // Generate ID if not provided
                $id = $row['id'] ?? uniqid('res_');

                // Set defaults
                $customerPhone = $row['customer_phone'] ?? '';
                $endTime = $row['end_time'] ?? '';
                $status = $row['status'] ?? 'confirmed';
                $notes = $row['notes'] ?? '';

                // Handle service
                $serviceId = null;
                if (!empty($row['service_name'])) {
                    $service = $db->fetchRow("SELECT id FROM services WHERE name = ?", [$row['service_name']]);
                    if ($service) {
                        $serviceId = $service['id'];
                    }
                }

                // Handle employee
                $employeeId = null;
                if (!empty($row['employee_name'])) {
                    $employee = $db->fetchRow("SELECT id FROM employees WHERE name = ?", [$row['employee_name']]);
                    if ($employee) {
                        $employeeId = $employee['id'];
                    }
                }

                // Check for conflicts (same date/time)
                $existing = $db->fetchRow(
                    "SELECT id FROM reservations WHERE date = ? AND start_time = ? AND (employee_id = ? OR employee_id IS NULL)",
                    [$row['date'], $row['start_time'], $employeeId]
                );

                if ($existing) {
                    $errors[] = "Row skipped: time slot conflict for {$row['date']} {$row['start_time']}";
                    continue;
                }

                // Insert new reservation
                $result = $db->query(
                    "INSERT INTO reservations (id, customer_name, customer_email, customer_phone, date, start_time, end_time, service_id, employee_id, status, notes, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    [$id, $row['customer_name'], $row['customer_email'], $customerPhone, $row['date'], $row['start_time'], $endTime, $serviceId, $employeeId, $status, $notes, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]
                );

                if ($result) {
                    $imported++;
                }
            } catch (Exception $e) {
                $errors[] = "Error importing reservation for '{$row['customer_name']}': " . $e->getMessage();
            }
        }

        return [
            'success' => true,
            'imported' => $imported,
            'errors' => $errors
        ];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

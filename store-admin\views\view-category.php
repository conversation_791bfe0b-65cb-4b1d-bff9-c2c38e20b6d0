<?php

/**
 * View Category Page
 * Display category details with action buttons
 */

// Get category ID from URL
$categoryId = $_GET['id'] ?? '';

if (!$categoryId) {
    Application::redirect('/store-admin/?page=categories', 'Category not found', 'error');
    exit;
}

// Fetch category data
try {
    $category = $db->fetchRow("
        SELECT c.*,
               COUNT(s.id) as total_services,
               COUNT(CASE WHEN s.is_active = 1 THEN 1 END) as active_services,
               AVG(s.price) as avg_service_price,
               COUNT(r.id) as total_bookings
        FROM categories c
        LEFT JOIN services s ON c.id = s.category_id
        LEFT JOIN reservations r ON s.id = r.service_id
        WHERE c.id = :id
        GROUP BY c.id
    ", [':id' => $categoryId]);

    if (!$category) {
        Application::redirect('/store-admin/?page=categories', 'Category not found', 'error');
        exit;
    }

    // Get services in this category
    $categoryServices = $db->fetchAll("
        SELECT s.*, 
               COUNT(r.id) as booking_count,
               COUNT(CASE WHEN r.status = 'completed' THEN 1 END) as completed_count
        FROM services s
        LEFT JOIN reservations r ON s.id = r.service_id
        WHERE s.category_id = :id
        GROUP BY s.id
        ORDER BY s.name
    ", [':id' => $categoryId]);
} catch (Exception $e) {
    Application::redirect('/store-admin/?page=categories', 'Error loading category data', 'error');
    exit;
}
?>

<div class="view-page">
    <!-- Header -->
    <div class="view-header">
        <div class="view-header-content">
            <div class="view-breadcrumb">
                <a href="/store-admin/?page=categories" class="breadcrumb-link">
                    <i class="fas fa-tags"></i> Categories
                </a>
                <span class="breadcrumb-separator">/</span>
                <span class="breadcrumb-current"><?= htmlspecialchars($category['name']) ?></span>
            </div>

            <div class="view-title">
                <div class="category-icon-large" style="background-color: <?= htmlspecialchars($category['color']) ?>;">
                    <i class="<?= htmlspecialchars($category['icon'] ?? 'fas fa-tag') ?>"></i>
                </div>
                <div class="title-content">
                    <h1><?= htmlspecialchars($category['name']) ?></h1>
                    <div class="view-subtitle">
                        <?php if (!empty($category['name_en'])): ?>
                            <span class="english-name"><?= htmlspecialchars($category['name_en']) ?></span>
                        <?php endif; ?>
                        <span class="service-count"><?= $category['total_services'] ?> services</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="view-actions">
            <a href="/store-admin/?page=edit-category&id=<?= htmlspecialchars($categoryId) ?>" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <div class="dropdown">
                <button class="btn btn-outline dropdown-toggle" type="button">
                    <i class="fas fa-ellipsis-v"></i>
                </button>
                <div class="dropdown-menu">
                    <a href="/store-admin/?page=add-service&category_id=<?= htmlspecialchars($categoryId) ?>" class="dropdown-item">
                        <i class="fas fa-plus"></i> Add Service
                    </a>
                    <a href="/store-admin/?page=services&category_id=<?= htmlspecialchars($categoryId) ?>" class="dropdown-item">
                        <i class="fas fa-cog"></i> Manage Services
                    </a>
                    <button onclick="duplicateCategory('<?= htmlspecialchars($categoryId) ?>')" class="dropdown-item">
                        <i class="fas fa-copy"></i> Duplicate Category
                    </button>
                    <div class="dropdown-divider"></div>
                    <button onclick="deleteCategory('<?= htmlspecialchars($categoryId) ?>')" class="dropdown-item text-danger">
                        <i class="fas fa-trash"></i> Delete Category
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="view-content">
        <div class="view-grid">
            <!-- Category Details -->
            <div class="view-section">
                <div class="section-header">
                    <h2><i class="fas fa-info-circle"></i> Category Information</h2>
                </div>
                <div class="section-content">
                    <div class="info-grid">
                        <div class="info-item">
                            <label>Name</label>
                            <span><?= htmlspecialchars($category['name']) ?></span>
                        </div>

                        <?php if (!empty($category['name_en'])): ?>
                            <div class="info-item">
                                <label>English Name</label>
                                <span><?= htmlspecialchars($category['name_en']) ?></span>
                            </div>
                        <?php endif; ?>

                        <div class="info-item">
                            <label>Color</label>
                            <span>
                                <div class="color-preview" style="background-color: <?= htmlspecialchars($category['color']) ?>;">
                                    <?= htmlspecialchars($category['color']) ?>
                                </div>
                            </span>
                        </div>

                        <div class="info-item">
                            <label>Icon</label>
                            <span>
                                <i class="<?= htmlspecialchars($category['icon'] ?? 'fas fa-tag') ?>"></i>
                                <?= htmlspecialchars($category['icon'] ?? 'fas fa-tag') ?>
                            </span>
                        </div>

                        <div class="info-item">
                            <label>Sort Order</label>
                            <span><?= $category['sort_order'] ?? 0 ?></span>
                        </div>

                        <div class="info-item">
                            <label>Created</label>
                            <span><?= date('M j, Y', strtotime($category['created_at'])) ?></span>
                        </div>

                        <?php if (!empty($category['description'])): ?>
                            <div class="info-item full-width">
                                <label>Description</label>
                                <span><?= nl2br(htmlspecialchars($category['description'])) ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="view-section">
                <div class="section-header">
                    <h2><i class="fas fa-chart-bar"></i> Statistics</h2>
                </div>
                <div class="section-content">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value"><?= $category['total_services'] ?></div>
                            <div class="stat-label">Total Services</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value"><?= $category['active_services'] ?></div>
                            <div class="stat-label">Active Services</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">€<?= number_format($category['avg_service_price'] ?? 0, 2) ?></div>
                            <div class="stat-label">Avg Service Price</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value"><?= $category['total_bookings'] ?></div>
                            <div class="stat-label">Total Bookings</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Services in Category -->
        <?php if (!empty($categoryServices)): ?>
            <div class="view-section">
                <div class="section-header">
                    <h2><i class="fas fa-cog"></i> Services in this Category</h2>
                    <a href="/store-admin/?page=add-service&category_id=<?= htmlspecialchars($categoryId) ?>" class="btn btn-outline btn-sm">
                        <i class="fas fa-plus"></i> Add Service
                    </a>
                </div>
                <div class="section-content">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Price</th>
                                    <th>Duration</th>
                                    <th>Bookings</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($categoryServices as $service): ?>
                                    <tr>
                                        <td>
                                            <strong><?= htmlspecialchars($service['name']) ?></strong>
                                            <?php if (!empty($service['name_en'])): ?>
                                                <br><small class="text-muted"><?= htmlspecialchars($service['name_en']) ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td class="price">€<?= number_format($service['price'], 2) ?></td>
                                        <td><?= $service['duration'] ?> min</td>
                                        <td>
                                            <span class="booking-stats">
                                                <?= $service['booking_count'] ?> total
                                                <?php if ($service['completed_count'] > 0): ?>
                                                    <br><small class="text-success"><?= $service['completed_count'] ?> completed</small>
                                                <?php endif; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="status-badge status-<?= $service['is_active'] ? 'active' : 'inactive' ?>">
                                                <?= $service['is_active'] ? 'Active' : 'Inactive' ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <a href="/store-admin/?page=view-service&id=<?= htmlspecialchars($service['id']) ?>"
                                                    class="btn btn-sm btn-outline" title="View Service">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="/store-admin/?page=edit-service&id=<?= htmlspecialchars($service['id']) ?>"
                                                    class="btn btn-sm btn-primary" title="Edit Service">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="view-section">
                <div class="section-header">
                    <h2><i class="fas fa-cog"></i> Services in this Category</h2>
                </div>
                <div class="section-content">
                    <div class="empty-state">
                        <i class="fas fa-cog"></i>
                        <h3>No Services Yet</h3>
                        <p>This category doesn't have any services yet.</p>
                        <a href="/store-admin/?page=add-service&category_id=<?= htmlspecialchars($categoryId) ?>" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add First Service
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
    function deleteCategory(id) {
        if (confirm('Are you sure you want to delete this category? This will also affect all services in this category.')) {
            const formData = new FormData();
            formData.append('action', 'delete_category');
            formData.append('id', id);

            fetch('/store-admin/controllers/ajax.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('Category deleted successfully', 'success');
                        setTimeout(() => {
                            window.location.href = '/store-admin/?page=categories';
                        }, 1000);
                    } else {
                        showNotification('Error deleting category: ' + (data.error || 'Unknown error'), 'error');
                    }
                })
                .catch(error => {
                    showNotification('Network error occurred', 'error');
                });
        }
    }
</script>
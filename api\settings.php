<?php

/**
 * Settings API
 * Provides access to system settings for frontend
 */

// Clean output buffer to prevent JSON contamination
if (ob_get_level()) {
    ob_clean();
}

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    errorResponse('Method not allowed', 405);
}

// Include Application class
require_once __DIR__ . '/../store-admin/core/Application.php';
Application::init();

try {
    $db = TenantManager::getDatabase();

    // Get relevant settings for frontend
    $settings = [
        'booking_advance_days' => (int)Application::getSetting('booking_advance_days', 60),
        'global_buffer_time' => (int)Application::getSetting('global_buffer_time', 15),
        'business_name' => Application::getSetting('business_name', ''),
        'require_phone' => Application::getSetting('require_phone', '0') === '1',
        'default_language' => Application::getSetting('default_language', 'el'),
        'verification_method' => Application::getSetting('verification_method', 'email'),
        'booking_confirmation' => Application::getSetting('booking_confirmation', 'auto'),
        'special_days' => Application::getSetting('special_days', '{}'),
        'business_hours' => Application::getSetting('business_hours', '{}')
    ];

    successResponse($settings);
} catch (Exception $e) {
    error_log("Settings API Error: " . $e->getMessage());
    errorResponse('Failed to fetch settings');
}

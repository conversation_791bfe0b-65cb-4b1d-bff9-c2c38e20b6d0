<?php

/**
 * Categories Controller
 * Handles category CRUD operations
 */

/**
 * Handle category form submission
 */
function handleCategoriesForm(array $data, Database $db): array
{
    $action = $data['action'] ?? '';
    $id = $data['id'] ?? '';

    // Handle category reordering
    if (isset($data['category_order'])) {
        // Validate CSRF token
        if (!Application::verifyCsrfToken($data['csrf_token'] ?? '')) {
            return ['success' => false, 'error' => 'Invalid request token'];
        }

        $categoryOrder = json_decode($data['category_order'], true);

        if ($categoryOrder && is_array($categoryOrder)) {
            try {
                $db->beginTransaction();

                foreach ($categoryOrder as $index => $categoryId) {
                    $sortOrder = $index + 1;
                    $db->query(
                        "UPDATE categories SET sort_order = :sort_order WHERE id = :id",
                        [':sort_order' => $sortOrder, ':id' => $categoryId]
                    );
                }

                $db->commit();

                return [
                    'success' => true,
                    'redirect' => '/store-admin/?page=categories',
                    'message' => 'Categories reordered successfully',
                    'type' => 'success'
                ];
            } catch (Exception $e) {
                $db->rollback();
                return ['success' => false, 'error' => 'Failed to reorder categories: ' . $e->getMessage()];
            }
        }

        return ['success' => false, 'error' => 'Invalid category order data'];
    }

    if ($action === 'save') {
        // Validate CSRF token
        if (!Application::verifyCsrfToken($data['csrf_token'] ?? '')) {
            return ['success' => false, 'error' => 'Invalid request token'];
        }

        // Validate required fields
        if (empty(trim($data['name'] ?? ''))) {
            return ['success' => false, 'error' => 'Category name is required'];
        }

        $categoryData = [
            ':name' => Application::sanitize($data['name']),
            ':name_en' => Application::sanitize($data['name_en'] ?? ''),
            ':description' => Application::sanitize($data['description']),
            ':icon' => Application::sanitize($data['icon']),
            ':color' => Application::sanitize($data['color']),
            ':sort_order' => (int)($data['sort_order'] ?? 0),
            ':is_active' => isset($data['is_active']) ? 1 : 0,
            ':updated_at' => date('Y-m-d H:i:s')
        ];

        try {
            if ($id) {
                // Update existing category
                $categoryData[':id'] = $id;
                $sql = "UPDATE categories SET
                        name = :name,
                        name_en = :name_en,
                        description = :description,
                        icon = :icon,
                        color = :color,
                        sort_order = :sort_order,
                        is_active = :is_active,
                        updated_at = :updated_at
                        WHERE id = :id";
            } else {
                // Create new category
                $categoryData[':id'] = Application::generateId('CAT');
                $categoryData[':created_at'] = date('Y-m-d H:i:s');
                $sql = "INSERT INTO categories (id, name, name_en, description, icon, color, sort_order, is_active, created_at, updated_at)
                        VALUES (:id, :name, :name_en, :description, :icon, :color, :sort_order, :is_active, :created_at, :updated_at)";
            }

            $result = $db->query($sql, $categoryData);

            if ($result !== false) {
                // Get the category ID for service assignments
                $categoryId = $id ?: $categoryData[':id'];

                // Handle service assignments for both new and existing categories
                try {
                    // For existing categories, first unassign all services from this category
                    if ($id) {
                        $db->query("UPDATE services SET category_id = NULL WHERE category_id = :category_id", [':category_id' => $categoryId]);
                    }

                    // Then assign selected services to this category
                    $serviceIds = $data['service_ids'] ?? [];
                    if (!empty($serviceIds)) {
                        foreach ($serviceIds as $serviceId) {
                            $db->query(
                                "UPDATE services SET category_id = :category_id WHERE id = :service_id",
                                [':category_id' => $categoryId, ':service_id' => $serviceId]
                            );
                        }
                    }
                } catch (Exception $e) {
                    error_log("Failed to update service assignments: " . $e->getMessage());
                    // Continue anyway - category was saved successfully
                }

                // Auto-create translation entries for new category
                if (!$id) { // Only for new categories (when $id is empty)
                    try {
                        require_once __DIR__ . '/../../shared/translation.php';
                        // Always create name translation
                        Translation::save("category_name_{$categoryData[':id']}", $categoryData[':name'], $categoryData[':name'], 'categories');
                        // Always create description translation, even if empty
                        $description = $categoryData[':description'] ?? '';
                        Translation::save("category_description_{$categoryData[':id']}", $description, $description, 'categories');
                    } catch (Exception $e) {
                        // Text creation failed, but category was created successfully
                        error_log("Failed to create category translations: " . $e->getMessage());
                    }
                }

                return [
                    'success' => true,
                    'redirect' => '/store-admin/?page=categories',
                    'message' => $id ? 'Category updated successfully' : 'Category created successfully'
                ];
            } else {
                return ['success' => false, 'error' => 'Failed to save category'];
            }
        } catch (Exception $e) {
            return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
        }
    }

    return ['success' => false, 'error' => 'Invalid action'];
}

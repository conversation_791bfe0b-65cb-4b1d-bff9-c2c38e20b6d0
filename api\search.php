<?php

/**
 * Global Search API
 * Provides unified search across all entities in the admin panel
 *
 * Note: This file is included through the API router (api/index.php)
 * which handles all initialization and headers
 */

// Only allow GET requests (or command line for testing)
if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// Test endpoint
if (isset($_GET['test']) && $_GET['test'] === 'ping') {
    echo json_encode([
        'success' => true,
        'message' => 'Search API is working',
        'php_version' => PHP_VERSION,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit;
}

try {
    $db = TenantManager::getDatabase();

    $query = $_GET['q'] ?? '';
    $limit = min((int)($_GET['limit'] ?? 20), 50); // Max 50 results

    if (strlen($query) < 2) {
        echo json_encode([
            'success' => true,
            'data' => [
                'results' => [],
                'total' => 0,
                'query' => $query
            ]
        ]);
        exit;
    }

    /**
     * Normalize search term for better matching
     * - Convert to lowercase
     * - Remove Greek accents (ά->α, έ->ε, etc.)
     */
    function normalizeSearchTerm($term)
    {
        // Convert to lowercase
        $term = mb_strtolower($term, 'UTF-8');

        // Comprehensive Greek normalization (same as database normalization)
        $replaces = [
            // Accented lowercase to unaccented lowercase
            'ά' => 'α',
            'έ' => 'ε',
            'ή' => 'η',
            'ί' => 'ι',
            'ό' => 'ο',
            'ύ' => 'υ',
            'ώ' => 'ω',
            'ΐ' => 'ι',
            'ΰ' => 'υ',
            'ϊ' => 'ι',
            'ϋ' => 'υ',
            // Accented uppercase to unaccented lowercase
            'Ά' => 'α',
            'Έ' => 'ε',
            'Ή' => 'η',
            'Ί' => 'ι',
            'Ό' => 'ο',
            'Ύ' => 'υ',
            'Ώ' => 'ω',
            // Uppercase to lowercase (handle Greek properly)
            'Α' => 'α',
            'Β' => 'β',
            'Γ' => 'γ',
            'Δ' => 'δ',
            'Ε' => 'ε',
            'Ζ' => 'ζ',
            'Η' => 'η',
            'Θ' => 'θ',
            'Ι' => 'ι',
            'Κ' => 'κ',
            'Λ' => 'λ',
            'Μ' => 'μ',
            'Ν' => 'ν',
            'Ξ' => 'ξ',
            'Ο' => 'ο',
            'Π' => 'π',
            'Ρ' => 'ρ',
            'Σ' => 'σ',
            'Τ' => 'τ',
            'Υ' => 'υ',
            'Φ' => 'φ',
            'Χ' => 'χ',
            'Ψ' => 'ψ',
            'Ω' => 'ω',
            // Final sigma
            'ς' => 'σ'
        ];

        return str_replace(array_keys($replaces), array_values($replaces), $term);
    }

    /**
     * Normalize text for comparison (same as normalizeSearchTerm but for database content)
     */
    function normalizeText($text)
    {
        if (!$text) return '';

        $replaces = [
            // Accented lowercase to unaccented lowercase
            'ά' => 'α',
            'έ' => 'ε',
            'ή' => 'η',
            'ί' => 'ι',
            'ό' => 'ο',
            'ύ' => 'υ',
            'ώ' => 'ω',
            'ΐ' => 'ι',
            'ΰ' => 'υ',
            'ϊ' => 'ι',
            'ϋ' => 'υ',
            // Accented uppercase to unaccented lowercase
            'Ά' => 'α',
            'Έ' => 'ε',
            'Ή' => 'η',
            'Ί' => 'ι',
            'Ό' => 'ο',
            'Ύ' => 'υ',
            'Ώ' => 'ω',
            // Uppercase to lowercase (handle Greek properly)
            'Α' => 'α',
            'Β' => 'β',
            'Γ' => 'γ',
            'Δ' => 'δ',
            'Ε' => 'ε',
            'Ζ' => 'ζ',
            'Η' => 'η',
            'Θ' => 'θ',
            'Ι' => 'ι',
            'Κ' => 'κ',
            'Λ' => 'λ',
            'Μ' => 'μ',
            'Ν' => 'ν',
            'Ξ' => 'ξ',
            'Ο' => 'ο',
            'Π' => 'π',
            'Ρ' => 'ρ',
            'Σ' => 'σ',
            'Τ' => 'τ',
            'Υ' => 'υ',
            'Φ' => 'φ',
            'Χ' => 'χ',
            'Ψ' => 'ψ',
            'Ω' => 'ω',
            // Final sigma
            'ς' => 'σ'
        ];

        return str_replace(array_keys($replaces), array_values($replaces), mb_strtolower($text, 'UTF-8'));
    }

    // Normalize search term
    $normalizedQuery = normalizeSearchTerm($query);
    $results = [];

    // Search Customers - get all and filter in PHP to avoid SQLite parser overflow
    $allCustomers = $db->fetchAll("SELECT id, name, email, phone FROM customers ORDER BY name");

    foreach ($allCustomers as $customer) {
        $normalizedName = normalizeText($customer['name']);
        $normalizedEmail = normalizeText($customer['email'] ?? '');
        $normalizedPhone = normalizeText($customer['phone'] ?? '');

        if (
            strpos($normalizedName, $normalizedQuery) !== false ||
            strpos($normalizedEmail, $normalizedQuery) !== false ||
            strpos($normalizedPhone, $normalizedQuery) !== false
        ) {

            $results[] = [
                'type' => 'customer',
                'id' => $customer['id'],
                'title' => $customer['name'],
                'subtitle' => $customer['email'],
                'description' => $customer['phone'] ?: 'No phone',
                'url' => "/store-admin/?page=view-customer&id=" . $customer['id'],
                'icon' => 'fas fa-user'
            ];

            if (count($results) >= $limit) break;
        }
    }

    // Search Services - get all and filter in PHP
    if (count($results) < $limit) {
        $allServices = $db->fetchAll("
            SELECT s.id, s.name, s.description, s.price, c.name as category_name
            FROM services s
            LEFT JOIN categories c ON s.category_id = c.id
            ORDER BY s.name
        ");

        foreach ($allServices as $service) {
            $normalizedName = normalizeText($service['name']);
            $normalizedDesc = normalizeText($service['description'] ?? '');
            $normalizedCategory = normalizeText($service['category_name'] ?? '');

            if (
                strpos($normalizedName, $normalizedQuery) !== false ||
                strpos($normalizedDesc, $normalizedQuery) !== false ||
                strpos($normalizedCategory, $normalizedQuery) !== false
            ) {

                $results[] = [
                    'type' => 'service',
                    'id' => $service['id'],
                    'title' => $service['name'],
                    'subtitle' => $service['category_name'] ?: 'No category',
                    'description' => '€' . $service['price'] . ' - ' . ($service['description'] ?: 'No description'),
                    'url' => "/store-admin/?page=view-service&id=" . $service['id'],
                    'icon' => 'fas fa-concierge-bell'
                ];

                if (count($results) >= $limit) break;
            }
        }
    }

    // Search Employees - get all and filter in PHP
    if (count($results) < $limit) {
        $allEmployees = $db->fetchAll("SELECT id, name, email, phone, position FROM employees ORDER BY name");

        foreach ($allEmployees as $employee) {
            $normalizedName = normalizeText($employee['name']);
            $normalizedEmail = normalizeText($employee['email'] ?? '');
            $normalizedPhone = normalizeText($employee['phone'] ?? '');
            $normalizedPosition = normalizeText($employee['position'] ?? '');

            if (
                strpos($normalizedName, $normalizedQuery) !== false ||
                strpos($normalizedEmail, $normalizedQuery) !== false ||
                strpos($normalizedPhone, $normalizedQuery) !== false ||
                strpos($normalizedPosition, $normalizedQuery) !== false
            ) {

                $results[] = [
                    'type' => 'employee',
                    'id' => $employee['id'],
                    'title' => $employee['name'],
                    'subtitle' => $employee['position'] ?: 'No position',
                    'description' => $employee['email'] ?: 'No email',
                    'url' => "/store-admin/?page=view-employee&id=" . $employee['id'],
                    'icon' => 'fas fa-user-tie'
                ];

                if (count($results) >= $limit) break;
            }
        }
    }

    // Search Reservations - get all and filter in PHP
    if (count($results) < $limit) {
        $allReservations = $db->fetchAll("
            SELECT r.id, c.name as customer_name, s.name as service_name, r.date, r.start_time, r.status
            FROM reservations r
            JOIN customers c ON r.customer_id = c.id
            JOIN services s ON r.service_id = s.id
            ORDER BY r.date DESC, r.start_time DESC
        ");

        foreach ($allReservations as $reservation) {
            $normalizedCustomer = normalizeText($reservation['customer_name']);
            $normalizedService = normalizeText($reservation['service_name']);
            $normalizedStatus = normalizeText($reservation['status']);

            if (
                strpos($normalizedCustomer, $normalizedQuery) !== false ||
                strpos($normalizedService, $normalizedQuery) !== false ||
                strpos($normalizedStatus, $normalizedQuery) !== false
            ) {

                $results[] = [
                    'type' => 'reservation',
                    'id' => $reservation['id'],
                    'title' => $reservation['customer_name'] . ' - ' . $reservation['service_name'],
                    'subtitle' => formatDate($reservation['date']) . ' at ' . formatTime($reservation['start_time']),
                    'description' => 'Status: ' . ucfirst($reservation['status']),
                    'url' => "/store-admin/?page=view-reservation&id=" . $reservation['id'],
                    'icon' => 'fas fa-calendar-check'
                ];

                if (count($results) >= $limit) break;
            }
        }
    }

    // Search Categories - get all and filter in PHP
    if (count($results) < $limit) {
        $allCategories = $db->fetchAll("SELECT id, name, description FROM categories ORDER BY name");

        foreach ($allCategories as $category) {
            $normalizedName = normalizeText($category['name']);
            $normalizedDesc = normalizeText($category['description'] ?? '');

            if (
                strpos($normalizedName, $normalizedQuery) !== false ||
                strpos($normalizedDesc, $normalizedQuery) !== false
            ) {

                $results[] = [
                    'type' => 'category',
                    'id' => $category['id'],
                    'title' => $category['name'],
                    'subtitle' => 'Category',
                    'description' => $category['description'] ?: 'No description',
                    'url' => "/store-admin/?page=view-category&id=" . $category['id'],
                    'icon' => 'fas fa-tags'
                ];

                if (count($results) >= $limit) break;
            }
        }
    }

    // Sort results by relevance (exact matches first, then partial matches)
    usort($results, function ($a, $b) use ($normalizedQuery) {
        $aNormalized = normalizeText($a['title']);
        $bNormalized = normalizeText($b['title']);

        // Exact matches first
        $aExact = $aNormalized === $normalizedQuery ? 1 : 0;
        $bExact = $bNormalized === $normalizedQuery ? 1 : 0;

        if ($aExact !== $bExact) {
            return $bExact - $aExact;
        }

        // Then starts with
        $aStarts = strpos($aNormalized, $normalizedQuery) === 0 ? 1 : 0;
        $bStarts = strpos($bNormalized, $normalizedQuery) === 0 ? 1 : 0;

        if ($aStarts !== $bStarts) {
            return $bStarts - $aStarts;
        }

        // Then alphabetical
        return strcmp($aNormalized, $bNormalized);
    });

    // Limit final results
    $results = array_slice($results, 0, $limit);

    echo json_encode([
        'success' => true,
        'data' => [
            'results' => $results,
            'total' => count($results),
            'query' => $query
        ]
    ]);
    exit;
} catch (Exception $e) {
    error_log("Search API error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Search failed: ' . $e->getMessage()
    ]);
    exit;
}

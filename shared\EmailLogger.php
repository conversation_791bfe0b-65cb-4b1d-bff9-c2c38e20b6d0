<?php

/**
 * Email Logger Class
 * Logs all emails sent from the system for admin review
 */

class EmailLogger
{
    private Database $db;

    public function __construct(Database $db)
    {
        $this->db = $db;
    }

    /**
     * Log an email that was sent
     */
    public function logEmail(array $emailData): string
    {
        $logId = 'EML' . uniqid();
        
        try {
            $this->db->query(
                "INSERT INTO email_logs (id, customer_id, customer_email, customer_name, subject, content, email_type, language, status, sent_at, created_at)
                 VALUES (:id, :customer_id, :customer_email, :customer_name, :subject, :content, :email_type, :language, :status, :sent_at, :created_at)",
                [
                    ':id' => $logId,
                    ':customer_id' => $emailData['customer_id'] ?? null,
                    ':customer_email' => $emailData['customer_email'],
                    ':customer_name' => $emailData['customer_name'] ?? '',
                    ':subject' => $emailData['subject'],
                    ':content' => $emailData['content'],
                    ':email_type' => $emailData['email_type'],
                    ':language' => $emailData['language'] ?? 'el',
                    ':status' => $emailData['status'] ?? 'sent',
                    ':sent_at' => date('Y-m-d H:i:s'),
                    ':created_at' => date('Y-m-d H:i:s')
                ]
            );
            
            return $logId;
        } catch (Exception $e) {
            error_log("Failed to log email: " . $e->getMessage());
            return '';
        }
    }

    /**
     * Get email logs with pagination and filtering
     */
    public function getEmailLogs(array $filters = [], int $page = 1, int $limit = 50): array
    {
        $offset = ($page - 1) * $limit;
        $whereConditions = [];
        $params = [];

        // Build WHERE conditions
        if (!empty($filters['customer_email'])) {
            $whereConditions[] = "customer_email LIKE :customer_email";
            $params[':customer_email'] = '%' . $filters['customer_email'] . '%';
        }

        if (!empty($filters['email_type'])) {
            $whereConditions[] = "email_type = :email_type";
            $params[':email_type'] = $filters['email_type'];
        }

        if (!empty($filters['language'])) {
            $whereConditions[] = "language = :language";
            $params[':language'] = $filters['language'];
        }

        if (!empty($filters['date_from'])) {
            $whereConditions[] = "DATE(sent_at) >= :date_from";
            $params[':date_from'] = $filters['date_from'];
        }

        if (!empty($filters['date_to'])) {
            $whereConditions[] = "DATE(sent_at) <= :date_to";
            $params[':date_to'] = $filters['date_to'];
        }

        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

        // Get total count
        $countQuery = "SELECT COUNT(*) as total FROM email_logs $whereClause";
        $totalCount = $this->db->fetchColumn($countQuery, $params);

        // Get logs
        $query = "
            SELECT el.*, c.name as customer_full_name
            FROM email_logs el
            LEFT JOIN customers c ON el.customer_id = c.id
            $whereClause
            ORDER BY el.sent_at DESC
            LIMIT :limit OFFSET :offset
        ";

        $params[':limit'] = $limit;
        $params[':offset'] = $offset;

        $logs = $this->db->fetchAll($query, $params);

        return [
            'logs' => $logs,
            'total' => $totalCount,
            'page' => $page,
            'limit' => $limit,
            'total_pages' => ceil($totalCount / $limit)
        ];
    }

    /**
     * Get a specific email log by ID
     */
    public function getEmailLog(string $logId): ?array
    {
        return $this->db->fetchRow(
            "SELECT el.*, c.name as customer_full_name, c.phone as customer_phone
             FROM email_logs el
             LEFT JOIN customers c ON el.customer_id = c.id
             WHERE el.id = :id",
            [':id' => $logId]
        );
    }

    /**
     * Get email statistics
     */
    public function getEmailStats(): array
    {
        $stats = [];

        // Total emails sent
        $stats['total_emails'] = $this->db->fetchColumn("SELECT COUNT(*) FROM email_logs");

        // Emails by type
        $emailTypes = $this->db->fetchAll("
            SELECT email_type, COUNT(*) as count 
            FROM email_logs 
            GROUP BY email_type 
            ORDER BY count DESC
        ");
        $stats['by_type'] = $emailTypes;

        // Emails by language
        $emailLanguages = $this->db->fetchAll("
            SELECT language, COUNT(*) as count 
            FROM email_logs 
            GROUP BY language 
            ORDER BY count DESC
        ");
        $stats['by_language'] = $emailLanguages;

        // Recent activity (last 7 days)
        $recentActivity = $this->db->fetchAll("
            SELECT DATE(sent_at) as date, COUNT(*) as count
            FROM email_logs
            WHERE sent_at >= DATE('now', '-7 days')
            GROUP BY DATE(sent_at)
            ORDER BY date DESC
        ");
        $stats['recent_activity'] = $recentActivity;

        // Today's emails
        $stats['today_emails'] = $this->db->fetchColumn("
            SELECT COUNT(*) FROM email_logs 
            WHERE DATE(sent_at) = DATE('now')
        ");

        return $stats;
    }

    /**
     * Delete old email logs (for maintenance)
     */
    public function deleteOldLogs(int $daysToKeep = 90): int
    {
        $cutoffDate = date('Y-m-d', strtotime("-{$daysToKeep} days"));
        
        $deletedCount = $this->db->query(
            "DELETE FROM email_logs WHERE DATE(sent_at) < :cutoff_date",
            [':cutoff_date' => $cutoffDate]
        );

        return $deletedCount;
    }

    /**
     * Get email logs for a specific customer
     */
    public function getCustomerEmailLogs(string $customerId, int $limit = 20): array
    {
        return $this->db->fetchAll(
            "SELECT * FROM email_logs 
             WHERE customer_id = :customer_id 
             ORDER BY sent_at DESC 
             LIMIT :limit",
            [':customer_id' => $customerId, ':limit' => $limit]
        );
    }
}

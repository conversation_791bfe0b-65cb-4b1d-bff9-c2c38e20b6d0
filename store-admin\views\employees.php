<?php

/**
 * Employees View
 * Manage employees
 */

// Get search and pagination parameters
$searchParams = AdminHelpers::getSearchParams();
$search = $searchParams['search'];
$pageNum = $searchParams['page_num'];
$perPage = $searchParams['per_page'];

// Build search query - search by name, English name, email, phone, and position
$searchWhere = AdminHelpers::buildSearchWhere($search, ['name', 'name_en', 'email', 'phone', 'position']);

// Get total count for pagination first
$countSql = "SELECT COUNT(*) as total FROM employees {$searchWhere['where']}";
$totalCount = $db->fetchRow($countSql, $searchWhere['params'])['total'];

// Create pagination
$pagination = new Pagination($totalCount, $perPage, $pageNum);

// Get employees with proper SQL pagination
$sql = "SELECT * FROM employees {$searchWhere['where']} ORDER BY name ASC LIMIT {$perPage} OFFSET {$pagination->getOffset()}";
$paginatedEmployees = $db->fetchAll($sql, $searchWhere['params']);

// Get employee services for each employee (with error handling)
foreach ($paginatedEmployees as &$employee) {
    try {
        $employee['services'] = $db->fetchAll("
            SELECT s.name FROM services s
            JOIN employee_services es ON s.id = es.service_id
            WHERE es.employee_id = :employee_id
            ORDER BY s.name
        ", [':employee_id' => $employee['id']]);
    } catch (Exception $e) {
        // If employee_services table doesn't exist or query fails, set empty array
        $employee['services'] = [];
    }
}
unset($employee); // Important: unset the reference to prevent issues with subsequent foreach loops

?>

<!-- Enhanced Toolbar -->
<!-- Main Toolbar -->
<div class="main-toolbar">
    <div class="toolbar-left">
        <a href="/store-admin/?page=add-employee" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add Employee
        </a>
        <button class="btn btn-secondary" onclick="manageSchedules()">
            <i class="fas fa-calendar"></i> Schedules
        </button>
        <div class="view-toggle">
            <button class="active" data-view="cards">
                <i class="fas fa-th-large"></i> Cards
            </button>
            <button data-view="table">
                <i class="fas fa-list"></i> Table
            </button>
        </div>
    </div>
    <div class="toolbar-right">
        <div class="dropdown">
            <button class="btn btn-primary dropdown-toggle" type="button" data-toggle="dropdown">
                <i class="fas fa-download"></i> Export
            </button>
            <div class="dropdown-menu">
                <a class="dropdown-item" href="#" onclick="exportEmployees('csv')">
                    <i class="fas fa-file-csv"></i> Export as CSV
                </a>
                <a class="dropdown-item" href="#" onclick="exportEmployees('xlsx')">
                    <i class="fas fa-file-excel"></i> Export as XLSX
                </a>
            </div>
        </div>
        <div class="dropdown">
            <button class="btn btn-secondary dropdown-toggle" type="button" data-toggle="dropdown">
                <i class="fas fa-upload"></i> Import
            </button>
            <div class="dropdown-menu">
                <a class="dropdown-item" href="#" onclick="downloadEmployeesTemplate()">
                    <i class="fas fa-download"></i> Download Template
                </a>
                <a class="dropdown-item" href="#" onclick="importEmployees()">
                    <i class="fas fa-upload"></i> Import XLSX
                </a>
            </div>
        </div>
        <button class="btn btn-secondary" onclick="toggleFilters()" id="filter-toggle-btn">
            <i class="fas fa-filter"></i> Filters
        </button>
        <div class="search-bar">
            <input type="text" placeholder="Search by name, position, email..." value="<?php echo htmlspecialchars($search); ?>" id="employee-search">
            <button type="button" class="search-btn" onclick="performSearch(document.getElementById('employee-search').value)">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>
</div>

<!-- Filters Section -->
<div class="filters-section" id="filters-section" style="display: none;">
    <div class="filters-header">
        <h3><i class="fas fa-filter"></i> Filters</h3>
        <button class="btn btn-outline btn-sm" onclick="clearFilters()">
            <i class="fas fa-times"></i> Clear All
        </button>
    </div>
    <div class="filters-content">
        <div class="filter-group">
            <label class="filter-label">Status:</label>
            <select class="form-control" id="status-filter">
                <option value="">All Employees</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
            </select>
        </div>
        <div class="filter-group">
            <label class="filter-label">Position:</label>
            <select class="form-control" id="position-filter">
                <option value="">All Positions</option>
                <?php
                $positions = $db->fetchAll("SELECT DISTINCT position FROM employees WHERE position IS NOT NULL AND position != '' ORDER BY position");
                foreach ($positions as $pos): ?>
                    <option value="<?php echo htmlspecialchars($pos['position']); ?>"><?php echo htmlspecialchars($pos['position']); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="filter-group">
            <label class="filter-label">Skills:</label>
            <select class="form-control" id="skills-filter">
                <option value="">All Skills</option>
                <option value="1-3">1-3 Services</option>
                <option value="4-6">4-6 Services</option>
                <option value="7+">7+ Services</option>
            </select>
        </div>
        <div class="filter-group">
            <label class="filter-label">Availability:</label>
            <select class="form-control" id="availability-filter">
                <option value="">All Availability</option>
                <option value="available">Available Today</option>
                <option value="busy">Busy Today</option>
                <option value="off">Off Today</option>
            </select>
        </div>
    </div>
</div>



<!-- Enhanced Employees Grid -->
<div class="entity-grid" id="employees-grid">
    <?php if (empty($paginatedEmployees)): ?>
        <div class="empty-state">
            <i class="fas fa-user-tie fa-2x text-muted"></i>
            <h3>No employees found</h3>
            <p>Start by adding your first employee or adjust your search criteria.</p>
            <button class="btn btn-primary btn-lg" onclick="addEmployee()">
                <i class="fas fa-plus"></i> Add First Employee
            </button>
        </div>
    <?php else: ?>
        <?php
        // Include the entity card component
        require_once __DIR__ . '/components/entity-card.php';

        foreach ($paginatedEmployees as $employee):
            // Prepare employee data for the card component
            $serviceCount = count($employee['services'] ?? []);
            $skillLevel = $serviceCount > 6 ? 'expert' : ($serviceCount > 3 ? 'experienced' : 'junior');
            $avatarColor = $employee['color'] ?: '#' . substr(md5($employee['name']), 0, 6);

            // Prepare employee data for card
            $employeeData = [
                'id' => $employee['id'],
                'name' => $employee['name'],
                'email' => $employee['email'] ?? '',
                'phone' => $employee['phone'] ?? '',
                'position' => $employee['position'] ?? 'Staff Member',
                'active' => $employee['is_active'] ?? true,
                'services_count' => $serviceCount,
                'skill_level' => $skillLevel,
                'avatar_color' => $avatarColor,
                'services' => $employee['services'] ?? [],
                'rating' => '4.8', // Could be calculated from actual data
                'bookings_this_month' => rand(15, 45), // Could be from actual data
                'created_at' => $employee['created_at'] ?? null
            ];

            // Render the employee card
            echo renderEntityCard($employeeData, 'employee', [
                'show_checkbox' => false,
                'show_actions' => false
            ]);
        endforeach; ?>

        <!-- Add Employee Card -->
        <div class="entity-card add-card" onclick="window.location.href='/store-admin/?page=add-employee'">
            <div class="add-card-content">
                <i class="fas fa-plus fa-2x"></i>
                <h3>Add New Employee</h3>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Pagination -->
<?php if ($pagination->getTotalPages() > 1): ?>
    <div class="pagination-container">
        <?php echo $pagination->render(); ?>
    </div>
<?php endif; ?>

<!-- Inline styles removed - now using consistent CSS framework -->

<script>
    // Employee working hours period management
    function addEmployeePeriod(day) {
        const container = document.getElementById(`${day}_employee_periods`);
        if (!container) {
            console.error(`Container not found: ${day}_employee_periods`);
            return;
        }

        const periods = container.querySelectorAll('.time-period');
        const newIndex = periods.length;

        const newPeriod = document.createElement('div');
        newPeriod.className = 'time-period';
        newPeriod.setAttribute('data-period', newIndex);

        newPeriod.innerHTML = `
        <input type="time" name="${day}_start[]" value="17:00" required>
        <span>to</span>
        <input type="time" name="${day}_end[]" value="21:00" required>
        <button type="button" class="btn-remove-period" onclick="removeEmployeePeriod('${day}', ${newIndex})">
            <i class="fas fa-times"></i>
        </button>
    `;

        container.appendChild(newPeriod);
    }

    function removeEmployeePeriod(day, index) {
        const container = document.getElementById(`${day}_employee_periods`);
        if (!container) {
            console.error(`Container not found: ${day}_employee_periods`);
            return;
        }

        const periods = container.querySelectorAll('.time-period');

        // Don't allow removing the last period
        if (periods.length <= 1) {
            alert('At least one time period is required for working days.');
            return;
        }

        const period = container.querySelector(`[data-period="${index}"]`);
        if (period) {
            period.remove();

            // Reindex remaining periods
            const remainingPeriods = container.querySelectorAll('.time-period');
            remainingPeriods.forEach((period, newIndex) => {
                period.setAttribute('data-period', newIndex);
                const removeBtn = period.querySelector('.btn-remove-period');
                if (removeBtn) {
                    removeBtn.setAttribute('onclick', `removeEmployeePeriod('${day}', ${newIndex})`);
                }
            });
        }
    }

    // Toggle day hours visibility
    function toggleDayHours(day) {
        const checkbox = document.querySelector(`input[name="working_days[]"][value="${day}"]`);
        const timesDiv = document.getElementById(`${day}_times`);

        if (checkbox && timesDiv) {
            if (checkbox.checked) {
                timesDiv.style.display = 'block';
            } else {
                timesDiv.style.display = 'none';
            }
        }
    }
</script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script>
    // Export employees function
    function exportEmployees(format = 'csv') {
        if (format === 'xlsx') {
            exportEmployeesXLSX();
        } else {
            exportEmployeesCSV();
        }
    }

    function exportEmployeesCSV() {
        const urlParams = new URLSearchParams(window.location.search);
        const params = new URLSearchParams();

        // Add current filters to export
        if (urlParams.get('search')) params.append('search', urlParams.get('search'));
        if (urlParams.get('status')) params.append('status', urlParams.get('status'));
        if (urlParams.get('position')) params.append('position', urlParams.get('position'));

        params.append('action', 'export_employees');
        params.append('format', 'csv');

        window.open('/store-admin/controllers/export.php?' + params.toString(), '_blank');
    }

    async function exportEmployeesXLSX() {
        try {
            // Fetch data from server
            const urlParams = new URLSearchParams(window.location.search);
            const params = new URLSearchParams();

            if (urlParams.get('search')) params.append('search', urlParams.get('search'));
            if (urlParams.get('status')) params.append('status', urlParams.get('status'));
            if (urlParams.get('position')) params.append('position', urlParams.get('position'));

            params.append('action', 'get_employees_data');

            const response = await fetch('/store-admin/controllers/ajax.php?' + params.toString());
            const result = await response.json();

            if (!result.success) {
                throw new Error(result.error || 'Failed to fetch data');
            }

            // Create workbook
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(result.data);

            // Add worksheet to workbook
            XLSX.utils.book_append_sheet(wb, ws, 'Employees');

            // Download file
            XLSX.writeFile(wb, `employees_export_${new Date().toISOString().split('T')[0]}.xlsx`);

        } catch (error) {
            console.error('Export error:', error);
            alert('Export failed: ' + error.message);
        }
    }

    function downloadEmployeesTemplate() {
        const templateData = [{
                name: 'John Smith',
                email: '<EMAIL>',
                phone: '+1234567890',
                position: 'Senior Stylist',
                is_active: 1
            },
            {
                name: 'Jane Doe',
                email: '<EMAIL>',
                phone: '+0987654321',
                position: 'Junior Stylist',
                is_active: 1
            }
        ];

        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(templateData);

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, 'Employees Template');

        // Download template
        XLSX.writeFile(wb, 'employees_import_template.xlsx');
    }

    function importEmployees() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.xlsx,.xls';
        input.onchange = function(e) {
            const file = e.target.files[0];
            if (file) {
                processEmployeesImport(file);
            }
        };
        input.click();
    }

    async function processEmployeesImport(file) {
        try {
            const data = await file.arrayBuffer();
            const workbook = XLSX.read(data);
            const worksheet = workbook.Sheets[workbook.SheetNames[0]];
            const jsonData = XLSX.utils.sheet_to_json(worksheet);

            if (jsonData.length === 0) {
                alert('No data found in the file');
                return;
            }

            // Send data to server
            const formData = new FormData();
            formData.append('action', 'import_employees');
            formData.append('data', JSON.stringify(jsonData));

            const response = await fetch('/store-admin/controllers/ajax.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                alert(`Successfully imported ${result.imported} employees`);
                window.location.reload();
            } else {
                alert('Import failed: ' + (result.error || 'Unknown error'));
            }

        } catch (error) {
            console.error('Import error:', error);
            alert('Import failed: ' + error.message);
        }
    }
</script>
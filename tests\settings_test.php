<?php

/**
 * Test Suite for Settings Fixes
 * Tests the four settings that were fixed:
 * 1. Notification emails setting
 * 2. Require phone setting  
 * 3. Default employee selection removal
 * 4. Global search functionality
 */

// Note: Initialization is handled by the calling script (tests/index.php)

class SettingsTest
{
    private $db;
    private $testResults = [];

    public function __construct()
    {
        $this->db = TenantManager::getDatabase();
    }

    public function runAllTests()
    {
        echo "<h1>Settings Test Suite - PHP " . PHP_VERSION . "</h1>\n";
        echo "<p>Testing on WAMP64 environment</p>\n";

        $this->testNotificationEmailsSetting();
        $this->testRequirePhoneSetting();
        $this->testDefaultEmployeeSelectionRemoval();
        $this->testGlobalSearchAPI();

        $this->displayResults();
    }

    /**
     * Test 1: Notification emails setting
     */
    public function testNotificationEmailsSetting()
    {
        echo "<h2>Test 1: Notification Emails Setting</h2>\n";

        // Test with setting disabled
        $this->setSetting('notification_emails', '0');

        // Create a test reservation
        $reservationId = $this->createTestReservation();

        // Capture output to check if email sending is skipped
        ob_start();
        $this->simulateConfirmationEmail($reservationId);
        $output = ob_get_clean();

        $this->addResult(
            'Notification emails disabled',
            strpos($output, 'notification emails disabled') !== false,
            'Should skip sending when disabled'
        );

        // Test with setting enabled
        $this->setSetting('notification_emails', '1');

        ob_start();
        $this->simulateConfirmationEmail($reservationId);
        $output = ob_get_clean();

        $this->addResult(
            'Notification emails enabled',
            strpos($output, 'notification emails disabled') === false,
            'Should attempt to send when enabled'
        );

        $this->cleanupTestReservation($reservationId);
    }

    /**
     * Test 2: Require phone setting
     */
    public function testRequirePhoneSetting()
    {
        echo "<h2>Test 2: Require Phone Setting</h2>\n";

        // Test backend validation with phone required
        $this->setSetting('require_phone', '1');

        $bookingData = [
            'service_id' => 1,
            'date' => date('Y-m-d', strtotime('+1 day')),
            'time' => '10:00',
            'customer_name' => 'Test Customer',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '' // Empty phone
        ];

        $result = $this->simulateBookingValidation($bookingData);
        $this->addResult(
            'Phone required - validation fails without phone',
            $result === 'Phone number is required',
            'Should require phone when setting is enabled'
        );

        // Test with phone provided
        $bookingData['customer_phone'] = '6912345678';
        $result = $this->simulateBookingValidation($bookingData);
        $this->addResult(
            'Phone required - validation passes with phone',
            $result !== 'Phone number is required',
            'Should pass validation when phone provided'
        );

        // Test with phone not required
        $this->setSetting('require_phone', '0');
        $bookingData['customer_phone'] = '';

        $result = $this->simulateBookingValidation($bookingData);
        $this->addResult(
            'Phone not required - validation passes without phone',
            $result !== 'Phone number is required',
            'Should not require phone when setting is disabled'
        );
    }

    /**
     * Test 3: Default employee selection removal
     */
    public function testDefaultEmployeeSelectionRemoval()
    {
        echo "<h2>Test 3: Default Employee Selection Removal</h2>\n";

        // Check that the setting is not in the settings array
        $settingsFile = file_get_contents(__DIR__ . '/../store-admin/views/settings.php');

        $this->addResult(
            'Default employee selection removed from settings view',
            strpos($settingsFile, 'default_employee_selection') === false,
            'Setting should not appear in settings page'
        );

        // Check that individual service settings still work
        $services = $this->db->fetchAll("SELECT employee_selection FROM services LIMIT 1");
        $this->addResult(
            'Individual service employee selection preserved',
            !empty($services),
            'Services should still have employee_selection field'
        );
    }

    /**
     * Test 4: Global search API
     */
    public function testGlobalSearchAPI()
    {
        echo "<h2>Test 4: Global Search API</h2>\n";

        // Test search API endpoint exists
        $searchFile = __DIR__ . '/../api/search.php';
        $this->addResult(
            'Search API file exists',
            file_exists($searchFile),
            'Search API endpoint should exist'
        );

        // Test search functionality
        $searchResults = $this->simulateSearchAPI('test');
        $this->addResult(
            'Search API returns results',
            is_array($searchResults) && isset($searchResults['results']),
            'Search should return structured results'
        );

        // Test empty search
        $emptyResults = $this->simulateSearchAPI('');
        $this->addResult(
            'Empty search returns empty results',
            is_array($emptyResults) && empty($emptyResults['results']),
            'Empty search should return no results'
        );
    }

    // Helper methods
    private function setSetting($key, $value)
    {
        $this->db->query(
            "INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES (?, ?, ?)",
            [$key, $value, date('Y-m-d H:i:s')]
        );
    }

    private function createTestReservation()
    {
        // Create test customer first
        $this->db->query(
            "INSERT INTO customers (name, email, phone, created_at) VALUES (?, ?, ?, ?)",
            ['Test Customer', '<EMAIL>', '6912345678', date('Y-m-d H:i:s')]
        );
        $customerId = $this->db->lastInsertId();

        // Create test reservation
        $this->db->query(
            "INSERT INTO reservations (customer_id, service_id, employee_id, date, start_time, status, created_at) 
             VALUES (?, 1, 1, ?, '10:00:00', 'confirmed', ?)",
            [$customerId, date('Y-m-d', strtotime('+1 day')), date('Y-m-d H:i:s')]
        );

        return $this->db->lastInsertId();
    }

    private function simulateConfirmationEmail($reservationId)
    {
        // Get reservation data
        $reservation = $this->db->fetchRow(
            "SELECT * FROM reservations WHERE id = ?",
            [$reservationId]
        );

        if (!$reservation) {
            echo "Reservation not found\n";
            return;
        }

        // Check notification setting
        $notificationEmailsEnabled = $this->db->fetchColumn(
            "SELECT value FROM settings WHERE key = 'notification_emails'"
        ) === '1';

        if (!$notificationEmailsEnabled) {
            echo "Booking confirmation email skipped - notification emails disabled\n";
            return;
        }

        echo "Attempting to send booking confirmation email\n";
    }

    private function simulateBookingValidation($data)
    {
        // Simulate the validation logic from api/booking.php
        $phoneRequired = $this->db->fetchColumn(
            "SELECT value FROM settings WHERE key = 'require_phone'"
        ) === '1';

        if (!$data['service_id'] || !$data['date'] || !$data['customer_name'] || !$data['customer_email']) {
            return 'Missing required fields';
        }

        if ($phoneRequired && empty($data['customer_phone'])) {
            return 'Phone number is required';
        }

        return 'Validation passed';
    }

    private function simulateSearchAPI($query)
    {
        if (strlen($query) < 2) {
            return ['results' => [], 'total' => 0, 'query' => $query];
        }

        $searchTerm = '%' . $query . '%';
        $results = [];

        // Search customers
        $customers = $this->db->fetchAll(
            "SELECT 'customer' as type, id, name, email FROM customers WHERE name LIKE ? LIMIT 5",
            [$searchTerm]
        );

        foreach ($customers as $customer) {
            $results[] = [
                'type' => 'customer',
                'id' => $customer['id'],
                'title' => $customer['name'],
                'subtitle' => $customer['email']
            ];
        }

        return ['results' => $results, 'total' => count($results), 'query' => $query];
    }

    private function cleanupTestReservation($reservationId)
    {
        $reservation = $this->db->fetchRow("SELECT customer_id FROM reservations WHERE id = ?", [$reservationId]);
        if ($reservation) {
            $this->db->query("DELETE FROM reservations WHERE id = ?", [$reservationId]);
            $this->db->query("DELETE FROM customers WHERE id = ?", [$reservation['customer_id']]);
        }
    }

    private function addResult($testName, $passed, $description)
    {
        $this->testResults[] = [
            'name' => $testName,
            'passed' => $passed,
            'description' => $description
        ];

        $status = $passed ? '✅ PASS' : '❌ FAIL';
        echo "<p><strong>{$status}</strong> - {$testName}: {$description}</p>\n";
    }

    private function displayResults()
    {
        $total = count($this->testResults);
        $passed = array_sum(array_column($this->testResults, 'passed'));
        $failed = $total - $passed;

        echo "<h2>Test Summary</h2>\n";
        echo "<p><strong>Total Tests:</strong> {$total}</p>\n";
        echo "<p><strong>Passed:</strong> {$passed}</p>\n";
        echo "<p><strong>Failed:</strong> {$failed}</p>\n";
        echo "<p><strong>Success Rate:</strong> " . round(($passed / $total) * 100, 1) . "%</p>\n";

        if ($failed > 0) {
            echo "<h3>Failed Tests:</h3>\n";
            foreach ($this->testResults as $result) {
                if (!$result['passed']) {
                    echo "<p>❌ {$result['name']}: {$result['description']}</p>\n";
                }
            }
        }
    }
}

// Run tests if accessed directly
if (basename($_SERVER['PHP_SELF']) === 'settings_test.php') {
    $test = new SettingsTest();
    $test->runAllTests();
}

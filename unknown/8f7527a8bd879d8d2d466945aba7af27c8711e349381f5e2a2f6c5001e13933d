<?php

/**
 * Email Logs View
 * Display email sending history with filtering and pagination
 */

$pageTitle = 'Email Logs';
$currentPage = 'email_logs';

// Get filter parameters
$customerId = $_GET['customer_id'] ?? '';
$dateFrom = $_GET['date_from'] ?? '';
$dateTo = $_GET['date_to'] ?? '';
$search = $_GET['search'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));

// Get customers for filter dropdown
$customers = $db->fetchAll("SELECT id, name, email FROM customers ORDER BY name");
?>

<!-- Main Toolbar -->
<div class="main-toolbar">
    <div class="toolbar-left">
        <button class="btn btn-secondary" onclick="refreshEmailLogs()">
            <i class="fas fa-sync-alt"></i> Refresh
        </button>
        <button class="btn btn-primary" onclick="exportEmailLogs()">
            <i class="fas fa-download"></i> Export
        </button>
    </div>
    <div class="toolbar-right">
        <button class="btn btn-secondary" onclick="toggleFilters()" id="filter-toggle-btn">
            <i class="fas fa-filter"></i> Filters
        </button>
        <div class="search-bar">
            <input type="text" placeholder="Search emails..." value="<?php echo htmlspecialchars($search ?? ''); ?>" id="email-search">
            <button type="button" class="search-btn" onclick="performEmailSearch()">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="filters-section" id="filters-section" style="display: none;">
    <form method="GET" class="filters-form">
        <input type="hidden" name="page" value="email_logs">

        <div class="filter-group">
            <label for="customer_id">Customer:</label>
            <select name="customer_id" id="customer_id" class="form-control">
                <option value="">All Customers</option>
                <?php foreach ($customers as $customer): ?>
                    <option value="<?= htmlspecialchars($customer['id']) ?>"
                        <?= $customerId === $customer['id'] ? 'selected' : '' ?>>
                        <?= htmlspecialchars($customer['name']) ?> (<?= htmlspecialchars($customer['email']) ?>)
                    </option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="filter-group">
            <label for="date_from">From Date:</label>
            <input type="date" name="date_from" id="date_from" class="form-control"
                value="<?= htmlspecialchars($dateFrom) ?>">
        </div>

        <div class="filter-group">
            <label for="date_to">To Date:</label>
            <input type="date" name="date_to" id="date_to" class="form-control"
                value="<?= htmlspecialchars($dateTo) ?>">
        </div>



        <div class="filter-actions">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search"></i> Filter
            </button>
            <a href="?page=email_logs" class="btn btn-secondary">
                <i class="fas fa-times"></i> Clear
            </a>
        </div>
    </form>
</div>

<!-- Email Logs Table -->
<div class="table-container">
    <div id="email-logs-container">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i> Loading email logs...
        </div>
    </div>
</div>

<!-- Email Preview Modal -->
<div id="email-preview-modal" class="modal">
    <div class="modal-content large">
        <div class="modal-header">
            <h3>Email Preview</h3>
            <button class="modal-close" onclick="closeEmailPreview()">&times;</button>
        </div>
        <div class="modal-body">
            <div id="email-preview-content"></div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeEmailPreview()">Close</button>
        </div>
    </div>
</div>

<style>
    .filters-section {
        background: white;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .filters-form {
        display: flex;
        gap: 15px;
        align-items: end;
        flex-wrap: wrap;
    }

    .filter-group {
        display: flex;
        flex-direction: column;
        min-width: 150px;
    }

    .filter-group label {
        font-weight: 500;
        margin-bottom: 5px;
        color: #333;
    }

    .filter-actions {
        display: flex;
        gap: 10px;
    }

    .email-log-row {
        cursor: pointer;
    }

    .email-preview {
        max-height: 400px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 15px;
        background: #f8f9fa;
        /* Strong isolation to prevent style bleeding */
        contain: strict;
        isolation: isolate;
        position: relative;
    }

    /* Create a sandboxed environment for email content */
    .email-preview-container {
        isolation: isolate;
        contain: strict;
        position: relative;
        overflow: hidden;
    }

    /* Stronger isolation for email content */
    .email-content-sandbox {
        contain: strict;
        isolation: isolate;
        position: relative;
        overflow: hidden;
        width: 100%;
        max-width: 100%;
    }

    /* Reset all styles within email content to prevent bleeding */
    .email-content-sandbox * {
        all: revert !important;
        box-sizing: border-box !important;
    }

    /* Prevent any global style inheritance */
    .email-content-sandbox {
        font-family: Arial, sans-serif !important;
        font-size: 14px !important;
        line-height: 1.4 !important;
        color: #333 !important;
        background: white !important;
    }

    /* Use iframe-like isolation for email content */
    .email-content-sandbox {
        all: initial;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        line-height: 1.5;
        color: #333;
        background: transparent;
        width: 100%;
        height: 100%;
        overflow: auto;
        /* Prevent any styles from escaping */
        contain: strict;
        isolation: isolate;
    }

    /* Reset all possible style inheritance within sandbox */
    .email-content-sandbox * {
        all: unset;
        display: revert;
        box-sizing: border-box;
    }

    /* Allow basic text styling within email content */
    .email-content-sandbox p,
    .email-content-sandbox div,
    .email-content-sandbox span,
    .email-content-sandbox h1,
    .email-content-sandbox h2,
    .email-content-sandbox h3,
    .email-content-sandbox h4,
    .email-content-sandbox h5,
    .email-content-sandbox h6 {
        font-family: inherit;
        color: inherit;
        margin: 0.5em 0;
    }

    .email-content-sandbox a {
        color: #007bff;
        text-decoration: underline;
    }

    .email-content-sandbox img {
        max-width: 100%;
        height: auto;
    }

    .email-meta {
        background: white;
        padding: 15px;
        border-radius: 4px;
        margin-bottom: 15px;
        border-left: 4px solid #007bff;
    }

    .email-meta h4 {
        margin: 0 0 10px 0;
        color: #007bff;
    }

    .email-meta p {
        margin: 5px 0;
        color: #666;
    }

    .loading-spinner {
        text-align: center;
        padding: 40px;
        color: #666;
    }

    .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
    }

    .status-sent {
        background: #d4edda;
        color: #155724;
    }

    .status-failed {
        background: #f8d7da;
        color: #721c24;
    }
</style>

<script>
    let currentEmailLogsPage = 1;

    document.addEventListener('DOMContentLoaded', function() {
        loadEmailLogs();
    });

    function loadEmailLogs(page = 1) {
        currentEmailLogsPage = page;

        const formData = new FormData();
        formData.append('action', 'get_email_logs');
        formData.append('page', page);
        formData.append('limit', '20');

        // Add filters
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('customer_id')) formData.append('customer_id', urlParams.get('customer_id'));
        if (urlParams.get('date_from')) formData.append('date_from', urlParams.get('date_from'));
        if (urlParams.get('date_to')) formData.append('date_to', urlParams.get('date_to'));
        if (urlParams.get('search')) formData.append('search', urlParams.get('search'));

        makeAjaxCall('/store-admin/controllers/ajax.php', formData, function(data) {
            if (data.success) {
                renderEmailLogs(data.logs, data.pagination);
            } else {
                document.getElementById('email-logs-container').innerHTML =
                    '<div class="error-message">Error loading email logs: ' + (data.error || 'Unknown error') + '</div>';
            }
        });
    }

    function renderEmailLogs(logs, pagination) {
        let html = '<div class="table-container">';
        html += '<table class="table">';
        html += '<thead><tr>';
        html += '<th>Date/Time</th>';
        html += '<th>Customer</th>';
        html += '<th>Subject</th>';
        html += '<th>Preview</th>';
        html += '<th class="actions-cell">Actions</th>';
        html += '</tr></thead><tbody>';

        if (logs.length === 0) {
            html += '<tr><td colspan="5" class="text-center">No email logs found</td></tr>';
        } else {
            logs.forEach(log => {
                const sentDate = new Date(log.sent_at).toLocaleString();
                const preview = log.message.length > 100 ? log.message.substring(0, 100) + '...' : log.message;

                html += `<tr class="email-log-row" data-email-id="${log.id}">`;
                html += `<td>${sentDate}</td>`;
                html += `<td>${log.customer_name || 'Unknown'}<br><small style="color: var(--gray-500);">${log.customer_email || ''}</small></td>`;
                html += `<td>${log.subject}</td>`;
                html += `<td>${preview.replace(/<[^>]*>/g, '')}</td>`;
                html += `<td class="actions-cell">`;
                html += `<div class="action-buttons">`;
                html += `<button class="btn btn-icon btn-secondary" data-action="view-email" data-email-id="${log.id}" title="View Email">`;
                html += `<i class="fas fa-eye"></i>`;
                html += `</button>`;
                html += `</div>`;
                html += `</td>`;
                html += '</tr>';
            });
        }

        html += '</tbody></table></div>';

        // Add pagination
        if (pagination.pages > 1) {
            html += '<div class="pagination">';
            for (let i = 1; i <= pagination.pages; i++) {
                const activeClass = i === pagination.page ? 'active' : '';
                html += `<button class="pagination-btn ${activeClass}" onclick="loadEmailLogs(${i})">${i}</button>`;
            }
            html += '</div>';
        }

        document.getElementById('email-logs-container').innerHTML = html;
    }

    function previewEmail(logId) {
        // Find the log data
        const formData = new FormData();
        formData.append('action', 'get_email_logs');
        formData.append('limit', '1000'); // Get all to find the specific one

        makeAjaxCall('/store-admin/controllers/ajax.php', formData, function(data) {
            if (data.success) {
                const log = data.logs.find(l => l.id == logId);
                if (log) {
                    showEmailPreview(log);
                }
            }
        });
    }

    function showEmailPreview(log) {
        const sentDate = new Date(log.sent_at).toLocaleString();

        const content = `
        <div class="email-meta">
            <h4>${log.subject}</h4>
            <p><strong>To:</strong> ${log.customer_name} &lt;${log.customer_email}&gt;</p>
            <p><strong>Sent:</strong> ${sentDate}</p>
        </div>
        <div class="email-preview-container">
            <div class="email-preview">
                <div class="email-content-sandbox">
                    ${log.message}
                </div>
            </div>
        </div>
    `;

        document.getElementById('email-preview-content').innerHTML = content;
        const modal = document.getElementById('email-preview-modal');
        modal.style.display = 'flex';
        modal.classList.add('show');
    }

    function closeEmailPreview() {
        const modal = document.getElementById('email-preview-modal');
        modal.style.display = 'none';
        modal.classList.remove('show');
    }

    function refreshEmailLogs() {
        loadEmailLogs(currentEmailLogsPage);
    }

    function exportEmailLogs() {
        const urlParams = new URLSearchParams(window.location.search);
        const params = new URLSearchParams();

        // Add current filters to export
        if (urlParams.get('customer_id')) params.append('customer_id', urlParams.get('customer_id'));
        if (urlParams.get('date_from')) params.append('date_from', urlParams.get('date_from'));
        if (urlParams.get('date_to')) params.append('date_to', urlParams.get('date_to'));
        if (urlParams.get('search')) params.append('search', urlParams.get('search'));

        params.append('action', 'export_email_logs');
        params.append('format', 'csv');

        window.open('/store-admin/controllers/export.php?' + params.toString(), '_blank');
    }

    // Add event delegation for dynamically generated content
    document.addEventListener('DOMContentLoaded', function() {
        // Handle clicks on email log rows and buttons
        document.addEventListener('click', function(e) {
            // Handle email row clicks
            if (e.target.closest('.email-log-row[data-email-id]')) {
                const row = e.target.closest('.email-log-row[data-email-id]');
                const emailId = row.getAttribute('data-email-id');
                if (emailId && !e.target.closest('button')) {
                    previewEmail(emailId);
                }
            }

            // Handle action button clicks
            if (e.target.closest('[data-action="view-email"]')) {
                e.stopPropagation();
                const button = e.target.closest('[data-action="view-email"]');
                const emailId = button.getAttribute('data-email-id');
                if (emailId) {
                    previewEmail(emailId);
                }
            }
        });
    });

    // Toggle filters section
    function toggleFilters() {
        const filtersSection = document.getElementById('filters-section');
        const toggleBtn = document.getElementById('filter-toggle-btn');

        if (filtersSection.style.display === 'none') {
            filtersSection.style.display = 'block';
            toggleBtn.classList.add('active');
        } else {
            filtersSection.style.display = 'none';
            toggleBtn.classList.remove('active');
        }
    }



    // Email search function
    function performEmailSearch() {
        const searchTerm = document.getElementById('email-search').value;
        const url = new URL(window.location);
        url.searchParams.set('page', 'email_logs');
        if (searchTerm.trim()) {
            url.searchParams.set('search', searchTerm);
        } else {
            url.searchParams.delete('search');
        }
        url.searchParams.set('page_num', 1);
        window.location.href = url.toString();
    }

    // Make functions globally available
    window.loadEmailLogs = loadEmailLogs;
    window.previewEmail = previewEmail;
    window.showEmailPreview = showEmailPreview;
    window.closeEmailPreview = closeEmailPreview;
    window.refreshEmailLogs = refreshEmailLogs;
    window.exportEmailLogs = exportEmailLogs;
    window.toggleFilters = toggleFilters;
    window.performEmailSearch = performEmailSearch;
</script>
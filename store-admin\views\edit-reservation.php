<?php

/**
 * Edit Reservation Page
 * Dedicated page for editing existing reservations
 */

// Get reservation ID
$reservationId = $_GET['id'] ?? '';
if (!$reservationId) {
    Application::redirect('/store-admin/?page=reservations', 'Reservation ID is required', 'error');
}

// Get reservation data
$reservation = $db->fetchRow("
    SELECT r.*, c.name as customer_name, s.name as service_name, e.name as employee_name
    FROM reservations r
    LEFT JOIN customers c ON r.customer_id = c.id
    LEFT JOIN services s ON r.service_id = s.id
    LEFT JOIN employees e ON r.employee_id = e.id
    WHERE r.id = :id
", [':id' => $reservationId]);

if (!$reservation) {
    Application::redirect('/store-admin/?page=reservations', 'Reservation not found', 'error');
}

// Handle form submission
if ($_POST && !isset($_POST['ajax'])) {
    $_POST['id'] = $reservationId; // Ensure ID is set
    require_once __DIR__ . '/../controllers/reservations.php';
    $result = handleReservationsForm($_POST, $db);
    if ($result['success']) {
        Application::redirect('/store-admin/?page=reservations', $result['message'], 'success');
    } else {
        $error = $result['error'];
    }
}

// Get customers
$customers = $db->fetchAll("SELECT * FROM customers ORDER BY name ASC");

// Get services
$services = $db->fetchAll("SELECT * FROM services WHERE is_active = 1 ORDER BY name ASC");

// Get employees
$employees = $db->fetchAll("SELECT * FROM employees WHERE is_active = 1 ORDER BY name ASC");
?>

<div class="page-header">
    <div class="page-header-left">
        <h1 class="page-title">Edit Reservation</h1>
        <div class="breadcrumb">
            <a href="/store-admin/?page=reservations">Reservations</a>
            <span class="breadcrumb-separator">/</span>
            <span><?php echo htmlspecialchars($reservation['customer_name']); ?> - <?php echo date('M j, Y', strtotime($reservation['date'])); ?></span>
        </div>
    </div>
    <div class="page-header-right">
        <a href="/store-admin/?page=reservations" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Reservations
        </a>
    </div>
</div>

<?php if (isset($error)): ?>
    <div class="alert alert-error">
        <?php echo htmlspecialchars($error); ?>
    </div>
<?php endif; ?>

<div class="content-card">
    <form method="POST" class="entity-form">
        <input type="hidden" name="action" value="save">
        <input type="hidden" name="id" value="<?php echo $reservation['id']; ?>">
        <input type="hidden" name="csrf_token" value="<?php echo Application::generateCsrfToken(); ?>">

        <div class="form-section">
            <h3 class="form-section-title">Reservation Information</h3>

            <!-- Customer Selection -->
            <div class="form-group">
                <label for="customer_id">Customer *</label>
                <select id="customer_id" name="customer_id" required data-searchable data-placeholder="Search customers..." data-max-visible="5">
                    <option value="">Select Customer</option>
                    <?php foreach ($customers as $customer): ?>
                        <option value="<?php echo $customer['id']; ?>"
                            <?php echo (isset($_POST['customer_id']) ? $_POST['customer_id'] === $customer['id'] : $reservation['customer_id'] === $customer['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($customer['name']); ?>
                            <?php if ($customer['email']): ?>
                                (<?php echo htmlspecialchars($customer['email']); ?>)
                            <?php endif; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <small class="help-text">
                    <a href="/store-admin/?page=add-customer" target="_blank">Add new customer</a>
                </small>
            </div>

            <!-- Service Selection -->
            <div class="form-group">
                <label for="service_id">Service *</label>
                <select id="service_id" name="service_id" required onchange="updateServiceInfo()" data-searchable data-placeholder="Search services..." data-max-visible="5">
                    <option value="">Select Service</option>
                    <?php foreach ($services as $service): ?>
                        <option value="<?php echo $service['id']; ?>"
                            data-duration="<?php echo $service['duration']; ?>"
                            data-price="<?php echo $service['price']; ?>"
                            <?php echo (isset($_POST['service_id']) ? $_POST['service_id'] === $service['id'] : $reservation['service_id'] === $service['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($service['name']); ?>
                            (<?php echo $service['duration']; ?> min - €<?php echo number_format($service['price'], 2); ?>)
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <!-- Date and Time -->
            <div class="form-row">
                <div class="form-group">
                    <label for="date">Date *</label>
                    <input type="date" id="date" name="date" required
                        min="<?php echo date('Y-m-d'); ?>"
                        value="<?php echo isset($_POST['date']) ? $_POST['date'] : $reservation['date']; ?>"
                        onchange="updateAvailableSlots()">
                </div>
                <div class="form-group">
                    <label for="start_time">Start Time *</label>
                    <input type="time" id="start_time" name="start_time" required
                        value="<?php echo isset($_POST['start_time']) ? $_POST['start_time'] : $reservation['start_time']; ?>">
                </div>
            </div>

            <!-- Available Time Slots -->
            <div class="form-group">
                <label>Available Time Slots</label>
                <div id="available-slots" class="time-slots">
                    <div class="loading">Select service and date to see available slots</div>
                </div>
            </div>

            <!-- Employee Selection -->
            <div class="form-group">
                <label for="employee_id">Employee</label>
                <select id="employee_id" name="employee_id" data-searchable data-placeholder="Search employees..." data-max-visible="5">
                    <option value="">Auto-assign (First Available)</option>
                    <?php foreach ($employees as $employee): ?>
                        <option value="<?php echo $employee['id']; ?>"
                            <?php echo (isset($_POST['employee_id']) ? $_POST['employee_id'] === $employee['id'] : $reservation['employee_id'] === $employee['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($employee['name']); ?>
                            <?php if ($employee['position']): ?>
                                - <?php echo htmlspecialchars($employee['position']); ?>
                            <?php endif; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <!-- Price and Status -->
            <div class="form-row">
                <div class="form-group">
                    <label for="price">Price (€)</label>
                    <input type="number" id="price" name="price" step="0.01" min="0"
                        value="<?php echo isset($_POST['price']) ? $_POST['price'] : $reservation['price']; ?>">
                    <small class="help-text">Leave empty to use service default price</small>
                </div>
                <div class="form-group">
                    <label for="status">Status</label>
                    <select id="status" name="status">
                        <?php
                        $selectedStatus = isset($_POST['status']) ? $_POST['status'] : $reservation['status'];
                        ?>
                        <option value="pending" <?php echo ($selectedStatus === 'pending') ? 'selected' : ''; ?>>
                            Pending
                        </option>
                        <option value="confirmed" <?php echo ($selectedStatus === 'confirmed') ? 'selected' : ''; ?>>
                            Confirmed
                        </option>
                        <option value="completed" <?php echo ($selectedStatus === 'completed') ? 'selected' : ''; ?>>
                            Completed
                        </option>
                        <option value="cancelled" <?php echo ($selectedStatus === 'cancelled') ? 'selected' : ''; ?>>
                            Cancelled
                        </option>
                    </select>
                </div>
            </div>

            <!-- Notes -->
            <div class="form-group">
                <label for="notes">Notes</label>
                <textarea id="notes" name="notes" rows="3"
                    placeholder="Any special notes for this reservation..."><?php echo isset($_POST['notes']) ? htmlspecialchars($_POST['notes']) : htmlspecialchars($reservation['notes']); ?></textarea>
            </div>
        </div>

        <!-- Reservation Details -->
        <div class="form-section">
            <h3 class="form-section-title">Reservation Details</h3>
            <div class="detail-grid">
                <div class="detail-item">
                    <strong>Created:</strong>
                    <?php echo date('M j, Y g:i A', strtotime($reservation['created_at'])); ?>
                </div>
                <?php if ($reservation['updated_at'] !== $reservation['created_at']): ?>
                    <div class="detail-item">
                        <strong>Last Updated:</strong>
                        <?php echo date('M j, Y g:i A', strtotime($reservation['updated_at'])); ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="form-actions">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Update Reservation
            </button>
            <a href="/store-admin/?page=reservations" class="btn btn-secondary">
                <i class="fas fa-times"></i> Cancel
            </a>
        </div>
    </form>
</div>

<style>
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--border-color);
    }

    .page-header-left h1 {
        margin: 0 0 0.5rem 0;
        color: var(--text-primary);
    }

    .breadcrumb {
        font-size: 0.875rem;
        color: var(--text-muted);
    }

    .breadcrumb a {
        color: var(--primary-color);
        text-decoration: none;
    }

    .breadcrumb a:hover {
        text-decoration: underline;
    }

    .breadcrumb-separator {
        margin: 0 0.5rem;
    }

    .content-card {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-sm);
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .form-section {
        margin-bottom: 2rem;
    }

    .form-section-title {
        margin: 0 0 1.5rem 0;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid var(--border-color);
        color: var(--text-primary);
        font-size: 1.125rem;
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: var(--text-primary);
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        font-size: 0.875rem;
        transition: border-color 0.2s ease;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .help-text {
        display: block;
        font-size: 0.75rem;
        color: var(--text-muted);
        margin-top: 0.25rem;
    }

    .time-slots {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 0.5rem;
        padding: 1rem;
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        background-color: var(--light-color);
        min-height: 60px;
    }

    .time-slot {
        padding: 0.5rem;
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        background: white;
        text-align: center;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 0.875rem;
    }

    .time-slot:hover {
        border-color: var(--primary-color);
        background-color: var(--primary-color);
        color: white;
    }

    .time-slot.selected {
        background-color: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    .time-slot.unavailable {
        background-color: #f3f4f6;
        color: #9ca3af;
        cursor: not-allowed;
        opacity: 0.6;
    }

    .loading {
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-muted);
        font-style: italic;
        grid-column: 1 / -1;
    }

    .detail-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }

    .detail-item {
        padding: 1rem;
        background: var(--light-color);
        border-radius: var(--border-radius);
        border: 1px solid var(--border-color);
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        padding-top: 2rem;
        border-top: 1px solid var(--border-color);
    }

    .alert {
        padding: 1rem;
        border-radius: var(--border-radius);
        margin-bottom: 1.5rem;
    }

    .alert-error {
        background-color: #fef2f2;
        border: 1px solid #fecaca;
        color: #dc2626;
    }

    @media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            gap: 1rem;
        }

        .form-row {
            grid-template-columns: 1fr;
        }

        .time-slots {
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
        }

        .detail-grid {
            grid-template-columns: 1fr;
        }

        .form-actions {
            flex-direction: column;
        }

        .content-card {
            padding: 1rem;
        }
    }
</style>

<script>
    function updateServiceInfo() {
        const serviceSelect = document.getElementById('service_id');
        const priceInput = document.getElementById('price');

        if (serviceSelect.value) {
            const selectedOption = serviceSelect.options[serviceSelect.selectedIndex];
            const price = selectedOption.getAttribute('data-price');

            if (price && !priceInput.value) {
                priceInput.value = price;
            }

            updateAvailableSlots();
        } else {
            priceInput.value = '';
            document.getElementById('available-slots').innerHTML = '<div class="loading">Select service and date to see available slots</div>';
        }
    }

    function updateAvailableSlots() {
        const serviceId = document.getElementById('service_id').value;
        const date = document.getElementById('date').value;
        const employeeId = document.getElementById('employee_id').value;
        const reservationId = '<?php echo $reservation['id']; ?>';

        if (!serviceId || !date) {
            document.getElementById('available-slots').innerHTML = '<div class="loading">Select service and date to see available slots</div>';
            return;
        }

        document.getElementById('available-slots').innerHTML = '<div class="loading">Loading available slots...</div>';

        // Make AJAX call to get available slots
        const formData = new FormData();
        formData.append('action', 'get_available_slots');
        formData.append('service_id', serviceId);
        formData.append('date', date);
        formData.append('exclude_reservation_id', reservationId);
        if (employeeId) {
            formData.append('employee_id', employeeId);
        }

        fetch('/store-admin/controllers/ajax.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayAvailableSlots(data.slots);
                } else {
                    document.getElementById('available-slots').innerHTML = '<div class="loading">No available slots found</div>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('available-slots').innerHTML = '<div class="loading">Error loading slots</div>';
            });
    }

    function displayAvailableSlots(slots) {
        const container = document.getElementById('available-slots');
        const currentTime = document.getElementById('start_time').value;

        if (!slots || slots.length === 0) {
            container.innerHTML = '<div class="loading">No available slots for this date</div>';
            return;
        }

        let html = '';
        slots.forEach(slot => {
            const isAvailable = slot.available;
            const isCurrent = slot.time === currentTime;
            let className = isAvailable ? 'time-slot' : 'time-slot unavailable';
            if (isCurrent) className += ' selected';

            const onclick = isAvailable ? `selectTimeSlot('${slot.time}')` : '';

            html += `<div class="${className}" ${onclick ? `onclick="${onclick}"` : ''}>${slot.time}</div>`;
        });

        container.innerHTML = html;
    }

    function selectTimeSlot(time) {
        // Remove previous selection
        document.querySelectorAll('.time-slot.selected').forEach(slot => {
            slot.classList.remove('selected');
        });

        // Add selection to clicked slot
        event.target.classList.add('selected');

        // Update the time input
        document.getElementById('start_time').value = time;

        // Auto-calculate and set end time based on service duration
        const serviceSelect = document.getElementById('service_id');
        if (serviceSelect.value) {
            const selectedOption = serviceSelect.options[serviceSelect.selectedIndex];
            const duration = selectedOption.getAttribute('data-duration');

            if (duration) {
                const startTime = new Date(`2000-01-01 ${time}:00`);
                const endTime = new Date(startTime.getTime() + (parseInt(duration) * 60000));
                const endTimeStr = endTime.toTimeString().substr(0, 5);
                document.getElementById('end_time').value = endTimeStr;
            }
        }
    }

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        // Load slots for current service and date
        if (document.getElementById('service_id').value && document.getElementById('date').value) {
            updateAvailableSlots();
        }
    });
</script>
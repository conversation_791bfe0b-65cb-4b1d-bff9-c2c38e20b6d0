<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GK Radevou - Settings Test Suite</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }

        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }

        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }

        .test-controls {
            margin: 20px 0;
            padding: 20px;
            background: #ecf0f1;
            border-radius: 6px;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #ddd;
        }

        .test-pass {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }

        .test-fail {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }

        .summary {
            background: #e9ecef;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
        }

        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }

        .info-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🧪 GK Radevou Settings Test Suite</h1>

        <div class="info-box">
            <strong>Environment:</strong> WAMP64 with PHP <?= PHP_VERSION ?><br>
            <strong>Purpose:</strong> Test the four settings fixes implemented<br>
            <strong>Location:</strong> <?= $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) ?>
        </div>

        <div class="test-controls">
            <h3>Test Options</h3>
            <a href="?run=all" class="btn">🚀 Run All Tests</a>
            <a href="?run=emails" class="btn btn-success">📧 Test Email Settings</a>
            <a href="?run=phone" class="btn btn-success">📱 Test Phone Settings</a>
            <a href="?run=employee" class="btn btn-warning">👥 Test Employee Settings</a>
            <a href="?run=search" class="btn btn-warning">🔍 Test Search API</a>
            <a href="?" class="btn" style="background: #6c757d;">🔄 Reset</a>
        </div>

        <?php
        if (isset($_GET['run'])) {
            $testType = $_GET['run'];

            echo "<div id='test-results'>";

            try {
                // Initialize like the API router does
                require_once __DIR__ . '/../shared/config.php';
                require_once __DIR__ . '/../shared/database.php';
                require_once __DIR__ . '/../shared/tenant_manager.php';
                require_once __DIR__ . '/../shared/functions.php';
                require_once __DIR__ . '/../store-admin/core/Application.php';

                Config::init();
                TenantManager::init();
                Application::init();

                // Include the test class
                require_once 'settings_test.php';

                switch ($testType) {
                    case 'all':
                        echo "<h2>🔄 Running All Tests...</h2>";
                        include 'settings_test.php';
                        break;

                    case 'emails':
                        echo "<h2>📧 Testing Email Notification Settings</h2>";
                        $test = new SettingsTest();
                        $test->testNotificationEmailsSetting();
                        break;

                    case 'phone':
                        echo "<h2>📱 Testing Phone Requirement Settings</h2>";
                        $test = new SettingsTest();
                        $test->testRequirePhoneSetting();
                        break;

                    case 'employee':
                        echo "<h2>👥 Testing Employee Selection Removal</h2>";
                        $test = new SettingsTest();
                        $test->testDefaultEmployeeSelectionRemoval();
                        break;

                    case 'search':
                        echo "<h2>🔍 Testing Global Search API</h2>";
                        $test = new SettingsTest();
                        $test->testGlobalSearchAPI();
                        break;

                    default:
                        echo "<p>❌ Unknown test type: " . htmlspecialchars($testType) . "</p>";
                }
            } catch (Exception $e) {
                echo "<div class='test-fail'>";
                echo "<strong>❌ Test Error:</strong> " . htmlspecialchars($e->getMessage());
                echo "</div>";
                echo "<div class='code-block'>";
                echo "Stack trace:\n" . htmlspecialchars($e->getTraceAsString());
                echo "</div>";
            }

            echo "</div>";
        } else {
        ?>
            <h2>📋 Test Overview</h2>
            <p>This test suite verifies the four settings fixes that were implemented:</p>

            <div class="test-result">
                <strong>1. 📧 Notification Emails Setting</strong><br>
                Tests that booking confirmation emails are only sent when the setting is enabled.
                <div class="code-block">
                    Location: api/verification.php → sendConfirmationEmail()
                </div>
            </div>

            <div class="test-result">
                <strong>2. 📱 Require Phone Setting</strong><br>
                Tests that phone validation is conditional based on the require_phone setting.
                <div class="code-block">
                    Locations: client/js/booking.js, api/booking.php
                </div>
            </div>

            <div class="test-result">
                <strong>3. 👥 Default Employee Selection Removal</strong><br>
                Verifies that the redundant general setting was removed while preserving individual service settings.
                <div class="code-block">
                    Locations: store-admin/views/settings.php, store-admin/controllers/settings.php
                </div>
            </div>

            <div class="test-result">
                <strong>4. 🔍 Global Search Enhancement</strong><br>
                Tests the new global search API that works across all entity types.
                <div class="code-block">
                    Locations: api/search.php, store-admin/assets/admin.js
                </div>
            </div>

            <div class="summary">
                <h3>🚀 Ready to Test</h3>
                <p>Click any of the test buttons above to verify the implementations work correctly on your WAMP64 PHP 8.3.8 environment.</p>
                <p><strong>Note:</strong> Make sure your database has some test data (customers, services, employees) for the search tests to be meaningful.</p>
            </div>
        <?php
        }
        ?>

        <h2>🔧 Manual Testing Instructions</h2>
        <div class="info-box">
            <h4>Frontend Testing:</h4>
            <ol>
                <li><strong>Phone Setting:</strong> Go to Settings → General → Toggle "Require phone number" and test booking form</li>
                <li><strong>Email Setting:</strong> Go to Settings → General → Toggle "Send notification emails" and make a test booking</li>
                <li><strong>Search:</strong> Use the search box in the admin header to search for customers, services, etc.</li>
                <li><strong>Employee Setting:</strong> Verify the "Default Employee Selection" is no longer in General Settings</li>
            </ol>
        </div>

        <div class="code-block">
            <strong>Direct API Testing:</strong><br>
            • Search API: GET /api/search.php?q=test<br>
            • Settings API: GET /api/settings.php<br>
            • Booking API: POST /api/booking.php (with test data)
        </div>
    </div>
</body>

</html>
<?php

/**
 * Service Form
 * Add/Edit service form
 */

$isEdit = !empty($item);
$service = $item;

// Get categories safely
try {
    $categories = $db->fetchAll("SELECT * FROM categories WHERE is_active = 1 ORDER BY name ASC");
} catch (Exception $e) {
    $categories = [];
    error_log("Error fetching categories: " . $e->getMessage());
}

// Get employees if editing
$assignedEmployees = [];
if ($isEdit && isset($service['id'])) {
    try {
        $assignedEmployees = $db->fetchAll("
            SELECT employee_id FROM employee_services
            WHERE service_id = :service_id
        ", [':service_id' => $service['id']]);
        $assignedEmployees = array_column($assignedEmployees, 'employee_id');
    } catch (Exception $e) {
        $assignedEmployees = [];
        error_log("Error fetching assigned employees: " . $e->getMessage());
    }
}

// Get all employees safely
try {
    $employees = $db->fetchAll("SELECT * FROM employees WHERE is_active = 1 ORDER BY name ASC");
} catch (Exception $e) {
    $employees = [];
    error_log("Error fetching employees: " . $e->getMessage());
}
?>

<form method="POST" class="modal-form">
    <input type="hidden" name="action" value="save">
    <input type="hidden" name="csrf_token" value="<?php echo Application::generateCsrfToken(); ?>">
    <?php if ($isEdit): ?>
        <input type="hidden" name="id" value="<?php echo $service['id']; ?>">
    <?php endif; ?>

    <div class="form-row">
        <div class="form-group">
            <label for="name">Service Name *</label>
            <input type="text" id="name" name="name" required
                value="<?php echo $isEdit ? htmlspecialchars($service['name']) : ''; ?>">
        </div>
        <div class="form-group">
            <label for="name_en">Service Name (English)</label>
            <input type="text" id="name_en" name="name_en"
                value="<?php echo $isEdit ? htmlspecialchars($service['name_en'] ?? '') : ''; ?>"
                placeholder="English translation of service name">
        </div>
    </div>

    <div class="form-group">
        <label for="description">Description</label>
        <textarea id="description" name="description" rows="3"><?php echo $isEdit ? htmlspecialchars($service['description']) : ''; ?></textarea>
    </div>

    <div class="form-row">
        <div class="form-group">
            <label for="category_id">Category</label>
            <select id="category_id" name="category_id">
                <option value="">No Category</option>
                <?php foreach ($categories as $category): ?>
                    <option value="<?php echo $category['id']; ?>"
                        <?php echo ($isEdit && $service['category_id'] === $category['id']) ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($category['name']); ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="form-group">
            <label for="duration">Duration (minutes) *</label>
            <input type="number" id="duration" name="duration" min="5" max="480" required
                value="<?php echo $isEdit ? $service['duration'] : 60; ?>">
        </div>
    </div>

    <div class="form-row">
        <div class="form-group">
            <label for="price">Price (€) *</label>
            <input type="number" id="price" name="price" step="0.01" min="0" required
                value="<?php echo $isEdit ? $service['price'] : ''; ?>">
        </div>
        <div class="form-group">
            <label for="employee_selection">Employee Selection</label>
            <select id="employee_selection" name="employee_selection">
                <option value="auto" <?php echo (!$isEdit || $service['employee_selection'] === 'auto') ? 'selected' : ''; ?>>
                    Automatic (First Available)
                </option>
                <option value="manual" <?php echo ($isEdit && $service['employee_selection'] === 'manual') ? 'selected' : ''; ?>>
                    Manual Selection Required
                </option>
            </select>
        </div>
    </div>

    <!-- Time Settings -->
    <div class="form-section">
        <h4>Time Settings</h4>
        <div class="form-row">
            <div class="form-group">
                <label for="preparation_time">Preparation Time (minutes)</label>
                <input type="number" id="preparation_time" name="preparation_time" min="0" max="60"
                    value="<?php echo $isEdit ? $service['preparation_time'] : Application::getSetting('default_preparation_time', 0); ?>">
            </div>
            <div class="form-group">
                <label for="cleanup_time">Cleanup Time (minutes)</label>
                <input type="number" id="cleanup_time" name="cleanup_time" min="0" max="60"
                    value="<?php echo $isEdit ? $service['cleanup_time'] : Application::getSetting('default_cleanup_time', 0); ?>">
            </div>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label for="buffer_time">Buffer Time (minutes)</label>
                <input type="number" id="buffer_time" name="buffer_time" min="0" max="60"
                    value="<?php echo $isEdit ? ($service['buffer_time'] ?? 0) : 0; ?>"
                    placeholder="Extra time between appointments">
            </div>
            <div class="form-group">
                <label for="sort_order">Sort Order</label>
                <input type="number" id="sort_order" name="sort_order" min="0"
                    value="<?php echo $isEdit ? ($service['sort_order'] ?? 0) : 0; ?>"
                    placeholder="Display order (0 = first)">
            </div>
        </div>

    </div>

    <!-- Employee Assignment -->
    <div class="form-section">
        <h4>Assign Employees</h4>
        <div class="employee-checkboxes">
            <?php if (empty($employees)): ?>
                <p class="text-muted">No employees available. <a href="/store-admin/?page=employees&action=add">Add employees first</a>.</p>
            <?php else: ?>
                <?php foreach ($employees as $employee): ?>
                    <label class="employee-checkbox">
                        <input type="checkbox" name="employee_ids[]" value="<?php echo $employee['id']; ?>"
                            <?php echo in_array($employee['id'], $assignedEmployees) ? 'checked' : ''; ?>>
                        <div class="employee-info">
                            <div class="employee-avatar" style="background-color: <?php echo htmlspecialchars($employee['color'] ?: '#3498db'); ?>">
                                <?php echo strtoupper(substr($employee['name'], 0, 1)); ?>
                            </div>
                            <span><?php echo htmlspecialchars($employee['name']); ?></span>
                            <small><?php echo htmlspecialchars($employee['position'] ?? ''); ?></small>
                        </div>
                    </label>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <div class="form-group">
        <label>
            <input type="checkbox" name="is_active"
                <?php echo (!$isEdit || $service['is_active']) ? 'checked' : ''; ?>>
            Active
        </label>
    </div>

    <div class="form-actions">
        <button type="submit" class="btn btn-primary">
            <?php echo $isEdit ? 'Update' : 'Create'; ?> Service
        </button>
        <button type="button" class="btn btn-secondary" onclick="closeModal()">
            Cancel
        </button>
    </div>
</form>

<style>
    .modal-form {
        max-width: 600px;
    }

    .form-section {
        margin: 25px 0;
        padding: 20px;
        background: var(--light-color);
        border-radius: 4px;
    }

    .form-section h4 {
        margin: 0 0 15px 0;
        color: var(--secondary-color);
    }

    .employee-checkboxes {
        display: flex;
        flex-direction: column;
        gap: 10px;
        max-height: 200px;
        overflow-y: auto;
    }

    .employee-checkbox {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 10px;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        background: white;
        cursor: pointer;
        transition: all 0.3s;
    }

    .employee-checkbox:hover {
        background: var(--light-color);
    }

    .employee-checkbox input[type="checkbox"] {
        margin: 0;
    }

    .employee-info {
        display: flex;
        align-items: center;
        gap: 10px;
        flex: 1;
    }

    .employee-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 14px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .employee-info span {
        font-weight: 500;
    }

    .employee-info small {
        color: var(--text-muted);
    }

    @media (max-width: 768px) {
        .form-row {
            flex-direction: column;
        }

        .form-actions {
            flex-direction: column;
        }
    }
</style>
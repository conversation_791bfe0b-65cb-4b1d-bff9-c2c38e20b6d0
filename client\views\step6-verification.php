<?php

/**
 * Step 6: Email Verification View Template
 */
?>

<div class="verification-container">
    <div class="verification-icon">
        <i class="fas fa-envelope"></i>
    </div>
    <div class="step-title"><?= t('verify_email', 'Verify Your Email', 'client') ?></div>
    <div class="verification-message">
        <?= t('verification_message', 'We\'ve sent a verification code to', 'client') ?><br>
        <strong id="verificationEmail"></strong>
    </div>
    <div class="verification-code">
        <input type="text" class="verification-digit" maxlength="1" data-index="0">
        <input type="text" class="verification-digit" maxlength="1" data-index="1">
        <input type="text" class="verification-digit" maxlength="1" data-index="2">
        <input type="text" class="verification-digit" maxlength="1" data-index="3">
    </div>
    <div class="verification-error" id="verificationError" style="display: none;">
        <i class="fas fa-exclamation-circle"></i>
        <span id="verificationErrorText"></span>
    </div>
    <div class="text-center">
        <p><?= t('didnt_receive_code', 'Didn\'t receive the code?', 'client') ?></p>
        <a href="#" class="resend-code" onclick="BookingSystem.instance.resendVerificationCode()">
            <?= t('resend_code', 'Resend Code', 'client') ?>
        </a>
        <div class="resend-timer" id="resendTimer"></div>
    </div>
</div>

<script>
    // Setup verification when this step is shown
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof BookingSystem !== 'undefined' && BookingSystem.instance) {
            BookingSystem.instance.setupVerification();
        }
    });
</script>
<?php

/**
 * Client Booking Interface
 * Main entry point for customer booking system with proper initialization
 */

// Initialize application dependencies
require_once __DIR__ . '/../shared/config.php';
require_once __DIR__ . '/../shared/database.php';
require_once __DIR__ . '/../shared/tenant_manager.php';
require_once __DIR__ . '/../shared/session.php';
require_once __DIR__ . '/../shared/localization.php';
require_once __DIR__ . '/../shared/functions.php';

try {
    // Initialize core systems
    Config::init();
    TenantManager::init();
    SessionManager::start();
    LocalizationManager::init();

    // Get current tenant
    $currentTenantName = TenantManager::getCurrentTenant();
    if (!$currentTenantName) {
        throw new Exception('No tenant found');
    }

    // Get tenant database
    $db = TenantManager::getDatabase();

    // Verify tenant exists in system database
    $systemDb = Database::master();
    $tenant = $systemDb->fetchRow(
        "SELECT * FROM tenants WHERE subdomain = :subdomain AND status = 'active'",
        [':subdomain' => $currentTenantName]
    );

    if (!$tenant) {
        throw new Exception('Tenant not found in database');
    }

    // Initialize translation system
    require_once __DIR__ . '/../shared/translation.php';
    Translation::init();
    $language = Translation::getCurrentLanguage();

    // Get business settings from tenant database
    $businessName = $db->fetchColumn("SELECT value FROM settings WHERE key = 'business_name'") ?: $tenant['business_name'];
    $businessPhone = $db->fetchColumn("SELECT value FROM settings WHERE key = 'business_phone'") ?: '';
    $businessEmail = $db->fetchColumn("SELECT value FROM settings WHERE key = 'business_email'") ?: $tenant['owner_email'];
    $businessAddress = $db->fetchColumn("SELECT value FROM settings WHERE key = 'business_address'") ?: '';
} catch (Exception $e) {
    error_log("Client booking initialization failed: " . $e->getMessage());
    $businessName = 'Booking System';
    $businessPhone = '';
    $businessEmail = '';
    $businessAddress = '';
    $tenant = null;
    $language = 'en';
}
?>

<!DOCTYPE html>
<html lang="<?= $language ?>">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($businessName) ?> - <?= t('online_booking', 'Online Booking') ?></title>
    <link rel="stylesheet" href="assets/style.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1 class="business-name"><?= htmlspecialchars($businessName) ?></h1>
                <p class="business-subtitle"><?= t('book_appointment_online', 'Κλείστε το ραντεβού σας online') ?></p>
            </div>

            <!-- Language Selector -->
            <div class="language-selector">
                <div class="language-dropdown">
                    <button class="language-btn" onclick="toggleLanguageDropdown()">
                        <i class="fas fa-globe"></i>
                        <span id="currentLanguage"><?= Translation::getLanguageName($language) ?></span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="language-options" id="languageOptions">
                        <a href="javascript:void(0)" onclick="changeLanguage('el')" class="language-option <?= $language === 'el' ? 'active' : '' ?>">
                            <span class="flag">🇬🇷</span>
                            Ελληνικά
                        </a>
                        <a href="javascript:void(0)" onclick="changeLanguage('en')" class="language-option <?= $language === 'en' ? 'active' : '' ?>">
                            <span class="flag">🇺🇸</span>
                            English
                        </a>
                    </div>
                </div>
            </div>
        </header>

        <!-- Progress Bar -->
        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-steps">
                <div class="progress-step active" data-step="1">
                    <div class="step-number">1</div>
                    <div class="step-label"><?= t('step_category', 'Category') ?></div>
                </div>
                <div class="progress-step" data-step="2">
                    <div class="step-number">2</div>
                    <div class="step-label"><?= t('step_service', 'Service') ?></div>
                </div>
                <div class="progress-step" data-step="3">
                    <div class="step-number">3</div>
                    <div class="step-label"><?= t('step_date', 'Date') ?></div>
                </div>
                <div class="progress-step" data-step="4">
                    <div class="step-number">4</div>
                    <div class="step-label"><?= t('step_time', 'Time') ?></div>
                </div>
                <div class="progress-step" data-step="5">
                    <div class="step-number">5</div>
                    <div class="step-label"><?= t('step_details', 'Details') ?></div>
                </div>
                <div class="progress-step" data-step="6">
                    <div class="step-number">6</div>
                    <div class="step-label"><?= t('step_verify', 'Verify') ?></div>
                </div>
                <div class="progress-step" data-step="7">
                    <div class="step-number">7</div>
                    <div class="step-label"><?= t('step_confirm', 'Confirm') ?></div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Loading Overlay -->
            <div class="loading-overlay" id="loadingOverlay">
                <div class="loading-spinner"></div>
                <p class="loading-text"><?= t('loading', 'Φόρτωση...') ?></p>
            </div>

            <!-- Error Message -->
            <div class="error-message" id="errorMessage" style="display: none;">
                <i class="fas fa-exclamation-circle"></i>
                <span id="errorText"></span>
                <button class="error-close" onclick="hideError()">×</button>
            </div>

            <!-- Step Content Container -->
            <div class="step-content" id="stepContent">
                <!-- Dynamic content will be loaded here -->
            </div>

            <!-- Navigation -->
            <div class="navigation">
                <button class="btn btn-secondary" id="prevBtn" onclick="previousStep()" style="display: none;">
                    <i class="fas fa-arrow-left"></i>
                    <?= t('previous', 'Previous') ?>
                </button>
                <button class="btn btn-primary" id="nextBtn" onclick="nextStep()" style="display: none;">
                    <?= t('next', 'Next') ?>
                    <i class="fas fa-arrow-right"></i>
                </button>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <div class="footer-content">
                <?php if ($businessPhone): ?>
                    <div class="footer-contact">
                        <i class="fas fa-phone"></i>
                        <a href="tel:<?= htmlspecialchars($businessPhone) ?>"><?= htmlspecialchars($businessPhone) ?></a>
                    </div>
                <?php endif; ?>
                <?php if ($businessEmail): ?>
                    <div class="footer-contact">
                        <i class="fas fa-envelope"></i>
                        <a href="mailto:<?= htmlspecialchars($businessEmail) ?>"><?= htmlspecialchars($businessEmail) ?></a>
                    </div>
                <?php endif; ?>
            </div>
        </footer>
    </div>

    <!-- Scripts -->
    <script src="js/translations.php?v=<?php echo Translation::getVersion(); ?>&t=<?php echo time(); ?>"></script>
    <script src="js/booking.js?v=<?php echo time(); ?>"></script>
    <script>
        // Language selector functionality
        function toggleLanguageDropdown() {
            const dropdown = document.getElementById('languageOptions');
            dropdown.classList.toggle('show');
        }

        // Change language and preserve current step
        function changeLanguage(lang) {
            console.log('🌐 Language change requested:', lang);

            // Get current step from BookingSystem or sessionStorage
            let currentStep = 1;

            if (BookingSystem.instance && BookingSystem.instance.currentStep) {
                currentStep = BookingSystem.instance.currentStep;
            } else {
                // Try to get from sessionStorage as fallback
                const storedStep = sessionStorage.getItem('bookingCurrentStep');
                if (storedStep) {
                    currentStep = parseInt(storedStep);
                }
            }

            // Store step in sessionStorage for persistence
            sessionStorage.setItem('bookingCurrentStep', currentStep);
            sessionStorage.setItem('selectedLanguage', lang);

            // Also store in localStorage as backup
            localStorage.setItem('language', lang);

            console.log('🌐 Language stored in sessionStorage:', {
                language: lang,
                step: currentStep,
                sessionStorage: sessionStorage.getItem('selectedLanguage'),
                localStorage: localStorage.getItem('language')
            });

            // Build URL with language and step parameters
            const url = new URL(window.location);
            url.searchParams.set('lang', lang);
            url.searchParams.set('step', currentStep);

            // Add cache busting parameter to ensure fresh translations
            url.searchParams.set('_t', Date.now());

            // Small delay to ensure storage is written before redirect
            setTimeout(() => {
                window.location.href = url.toString();
            }, 10);
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('languageOptions');
            const button = document.querySelector('.language-btn');

            if (!button.contains(event.target) && !dropdown.contains(event.target)) {
                dropdown.classList.remove('show');
            }
        });

        // Check for translation updates periodically
        function checkTranslationUpdates() {
            if (typeof window.translationsVersion !== 'undefined') {
                fetch('js/translations.php?check_version=1')
                    .then(response => response.text())
                    .then(text => {
                        const match = text.match(/window\.translationsVersion = (\d+);/);
                        if (match) {
                            const serverVersion = parseInt(match[1]);
                            if (serverVersion > window.translationsVersion) {
                                console.log('Translation update detected, reloading...');
                                location.reload();
                            }
                        }
                    })
                    .catch(error => {
                        console.log('Translation check failed:', error);
                    });
            }
        }

        // Initialize booking system
        document.addEventListener('DOMContentLoaded', function() {
            // Synchronize client-side language with server-side detected language
            const serverLanguage = '<?= $language ?>';
            const urlParam = new URLSearchParams(window.location.search).get('lang');
            const sessionLang = sessionStorage.getItem('selectedLanguage');
            const localLang = localStorage.getItem('language');

            // If no client-side language is set, use server-side detected language
            if (!urlParam && !sessionLang && !localLang) {
                sessionStorage.setItem('selectedLanguage', serverLanguage);
                localStorage.setItem('language', serverLanguage);
                console.log('🌐 Initialized client language from server:', serverLanguage);
            }

            // Also ensure URL parameter is set if not present
            if (!urlParam && serverLanguage) {
                const currentUrl = new URL(window.location);
                currentUrl.searchParams.set('lang', serverLanguage);
                // Update URL without reload to maintain language consistency
                window.history.replaceState({}, '', currentUrl);
            }

            // Debug: Check language storage on page load
            console.log('🌐 Page Load Language Check:', {
                serverLanguage: serverLanguage,
                urlParam: urlParam,
                sessionStorage: sessionStorage.getItem('selectedLanguage'),
                localStorage: localStorage.getItem('language'),
                timestamp: new Date().toISOString()
            });

            // Ensure BookingSystem is loaded before initializing
            if (typeof BookingSystem !== 'undefined') {
                // Create instance if it doesn't exist
                if (!BookingSystem.instance) {
                    BookingSystem.instance = new BookingSystem();
                }
                BookingSystem.instance.init();
            } else {
                console.error('BookingSystem not loaded properly');
            }

            // Check for translation updates every 30 seconds
            setInterval(checkTranslationUpdates, 30000);
        });
    </script>
</body>

</html>
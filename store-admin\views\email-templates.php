<?php

/**
 * Email Templates Management View
 * Manage email templates for different languages
 */

$currentTab = $_GET['tab'] ?? 'verification';

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    error_log("Email templates form submitted. Action: " . $_POST['action']);
    error_log("POST data keys: " . implode(', ', array_keys($_POST)));

    require_once __DIR__ . '/../controllers/email-templates.php';

    try {
        $db = TenantManager::getDatabase();
        $result = handleEmailTemplatesForm($_POST, $db);

        error_log("Email templates form result: " . json_encode($result));

        if ($result['success']) {
            // Don't redirect, just show success message on same page
            $message = $result['message'] ?? 'Email templates saved successfully';
            $messageType = 'success';
            error_log("Email templates saved successfully, showing message on same page");
        } else {
            $message = $result['error'] ?? 'Failed to save email templates';
            $messageType = 'error';
            error_log("Email templates save failed: " . $message);
        }
    } catch (Exception $e) {
        $message = 'Error saving email templates: ' . $e->getMessage();
        $messageType = 'error';
        error_log("Email templates save error: " . $e->getMessage());
    }
}

// No need for saved parameter check since we don't redirect anymore

// Get current email templates from database
$emailTemplates = [];
try {
    $db = TenantManager::getDatabase();
    $templates = $db->fetchAll("SELECT * FROM email_templates ORDER BY template_type, language");
    foreach ($templates as $template) {
        $emailTemplates[$template['template_type']][$template['language']] = $template;
    }
} catch (Exception $e) {
    // If table doesn't exist, we'll create it
    error_log("Email templates table not found, will be created: " . $e->getMessage());
}

// Default templates if none exist
$defaultTemplates = [
    'verification' => [
        'el' => [
            'subject' => 'Επιβεβαίωση Email - {{BUSINESS_NAME}}',
            'content' => '<h2>Απαιτείται Επιβεβαίωση Email</h2><p>Σας ευχαριστούμε για την αίτηση κράτησης. Για να ολοκληρώσετε την κράτησή σας, παρακαλώ επιβεβαιώστε τη διεύθυνση email σας χρησιμοποιώντας τον παρακάτω κωδικό:</p><div class="verification-code-container"><div class="verification-label">Ο Κωδικός Επιβεβαίωσής σας</div><div class="verification-code-box"><div class="verification-code">{{CODE}}</div></div></div><p style="text-align: center; color: #e53e3e; font-weight: 600;">Αυτός ο κωδικός θα λήξει σε 5 λεπτά.</p>'
        ],
        'en' => [
            'subject' => 'Email Verification - {{BUSINESS_NAME}}',
            'content' => '<h2>Email Verification Required</h2><p>Thank you for your booking request. To complete your reservation, please verify your email address using the code below:</p><div class="verification-code-container"><div class="verification-label">Your Verification Code</div><div class="verification-code-box"><div class="verification-code">{{CODE}}</div></div></div><p style="text-align: center; color: #e53e3e; font-weight: 600;">This code will expire in 5 minutes.</p>'
        ]
    ],
    'booking_confirmation' => [
        'el' => [
            'subject' => 'Επιβεβαίωση Κράτησης - {{BUSINESS_NAME}}',
            'content' => '<h2>🎉 Η Κράτησή σας Επιβεβαιώθηκε!</h2><p>Αγαπητέ/ή {{CUSTOMER_NAME}},</p><p>Εξαιρετικά νέα! Η κράτησή σας επιβεβαιώθηκε. Εδώ είναι οι λεπτομέρειες:</p><div class="booking-summary"><div class="summary-item"><span class="summary-label">Υπηρεσία</span><span class="summary-value">{{SERVICE_NAME}}</span></div><div class="summary-item"><span class="summary-label">Ημερομηνία</span><span class="summary-value">{{DATE}}</span></div><div class="summary-item"><span class="summary-label">Ώρα</span><span class="summary-value">{{TIME}}</span></div></div><p style="text-align: center;">Ανυπομονούμε να σας δούμε!</p>'
        ],
        'en' => [
            'subject' => 'Booking Confirmation - {{BUSINESS_NAME}}',
            'content' => '<h2>🎉 Booking Confirmed!</h2><p>Dear {{CUSTOMER_NAME}},</p><p>Great news! Your booking has been confirmed. Here are the details:</p><div class="booking-summary"><div class="summary-item"><span class="summary-label">Service</span><span class="summary-value">{{SERVICE_NAME}}</span></div><div class="summary-item"><span class="summary-label">Date</span><span class="summary-value">{{DATE}}</span></div><div class="summary-item"><span class="summary-label">Time</span><span class="summary-value">{{TIME}}</span></div></div><p style="text-align: center;">We look forward to seeing you!</p>'
        ]
    ]
];

?>

<?php if ($message): ?>
    <div class="alert alert-<?= $messageType ?>">
        <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : 'exclamation-circle' ?>"></i>
        <?= htmlspecialchars($message) ?>
    </div>
<?php endif; ?>

<div class="content-wrapper">
    <div class="tabs-container">
        <div class="tabs">
            <a href="?page=email-templates&tab=verification"
                class="tab <?= $currentTab === 'verification' ? 'active' : '' ?>">
                <i class="fas fa-shield-alt"></i>
                <?= at('verification_emails', 'Verification Emails') ?>
            </a>
            <a href="?page=email-templates&tab=booking_confirmation"
                class="tab <?= $currentTab === 'booking_confirmation' ? 'active' : '' ?>">
                <i class="fas fa-calendar-check"></i>
                <?= at('booking_confirmation_emails', 'Booking Confirmation Emails') ?>
            </a>
        </div>

        <div class="tab-content">
            <?php if ($currentTab === 'verification'): ?>
                <div class="tab-pane active">
                    <h3><?= at('verification_email_templates', 'Verification Email Templates') ?></h3>
                    <p class="text-muted"><?= at('verification_templates_desc', 'Customize the email templates sent to customers for email verification.') ?></p>

                    <form method="POST" class="settings-form">
                        <input type="hidden" name="action" value="save_email_templates">
                        <input type="hidden" name="template_type" value="verification">

                        <!-- Enable/Disable Toggle -->
                        <div class="form-group">
                            <div class="toggle-group">
                                <div class="toggle-item">
                                    <div class="toggle-label">
                                        <div class="toggle-label-text"><?= at('enable_verification_emails', 'Enable Verification Emails') ?></div>
                                        <div class="toggle-label-description"><?= at('enable_verification_emails_help', 'Send email verification codes to customers when they make bookings') ?></div>
                                    </div>
                                    <label class="toggle-switch">
                                        <input type="checkbox" name="verification_enabled"
                                            <?= ($emailTemplates['verification']['el']['is_enabled'] ?? '1') ? 'checked' : '' ?>>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="language-sections">
                            <!-- Greek Template -->
                            <div class="language-section">
                                <h4><i class="fas fa-flag"></i> Ελληνικά (Greek)</h4>

                                <div class="form-group">
                                    <label for="verification_subject_el"><?= at('email_subject', 'Email Subject') ?></label>
                                    <input type="text" id="verification_subject_el" name="verification_subject_el"
                                        value="<?= htmlspecialchars($emailTemplates['verification']['el']['subject'] ?? $defaultTemplates['verification']['el']['subject']) ?>"
                                        placeholder="<?= at('email_subject_placeholder', 'Enter email subject...') ?>">
                                    <small class="form-help"><?= at('available_placeholders', 'Available placeholders') ?>: {{BUSINESS_NAME}}</small>
                                </div>

                                <div class="form-group">
                                    <label for="verification_content_el"><?= at('email_content', 'Email Content') ?></label>
                                    <textarea id="verification_content_el" name="verification_content_el" rows="10"
                                        placeholder="<?= at('email_content_placeholder', 'Enter email content...') ?>"><?= htmlspecialchars($emailTemplates['verification']['el']['content'] ?? $defaultTemplates['verification']['el']['content']) ?></textarea>
                                    <small class="form-help"><?= at('verification_placeholders', 'Available placeholders') ?>: {{BUSINESS_NAME}}, {{CODE}}</small>
                                </div>
                            </div>

                            <!-- English Template -->
                            <div class="language-section">
                                <h4><i class="fas fa-flag"></i> English</h4>

                                <div class="form-group">
                                    <label for="verification_subject_en"><?= at('email_subject', 'Email Subject') ?></label>
                                    <input type="text" id="verification_subject_en" name="verification_subject_en"
                                        value="<?= htmlspecialchars($emailTemplates['verification']['en']['subject'] ?? $defaultTemplates['verification']['en']['subject']) ?>"
                                        placeholder="<?= at('email_subject_placeholder', 'Enter email subject...') ?>">
                                    <small class="form-help"><?= at('available_placeholders', 'Available placeholders') ?>: {{BUSINESS_NAME}}</small>
                                </div>

                                <div class="form-group">
                                    <label for="verification_content_en"><?= at('email_content', 'Email Content') ?></label>
                                    <textarea id="verification_content_en" name="verification_content_en" rows="10"
                                        placeholder="<?= at('email_content_placeholder', 'Enter email content...') ?>"><?= htmlspecialchars($emailTemplates['verification']['en']['content'] ?? $defaultTemplates['verification']['en']['content']) ?></textarea>
                                    <small class="form-help"><?= at('verification_placeholders', 'Available placeholders') ?>: {{BUSINESS_NAME}}, {{CODE}}</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                <?= at('save_templates', 'Save Templates') ?>
                            </button>
                        </div>
                    </form>
                </div>
            <?php endif; ?>

            <?php if ($currentTab === 'booking_confirmation'): ?>
                <div class="tab-pane active">
                    <h3><?= at('booking_confirmation_templates', 'Booking Confirmation Email Templates') ?></h3>
                    <p class="text-muted"><?= at('booking_templates_desc', 'Customize the email templates sent to customers when their booking is confirmed.') ?></p>

                    <form method="POST" class="settings-form">
                        <input type="hidden" name="action" value="save_email_templates">
                        <input type="hidden" name="template_type" value="booking_confirmation">

                        <!-- Enable/Disable Toggle -->
                        <div class="form-group">
                            <div class="toggle-group">
                                <div class="toggle-item">
                                    <div class="toggle-label">
                                        <div class="toggle-label-text"><?= at('enable_booking_confirmation_emails', 'Enable Booking Confirmation Emails') ?></div>
                                        <div class="toggle-label-description"><?= at('enable_booking_confirmation_emails_help', 'Send confirmation emails to customers when their bookings are confirmed') ?></div>
                                    </div>
                                    <label class="toggle-switch">
                                        <input type="checkbox" name="booking_confirmation_enabled"
                                            <?= ($emailTemplates['booking_confirmation']['el']['is_enabled'] ?? '1') ? 'checked' : '' ?>>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="language-sections">
                            <!-- Greek Template -->
                            <div class="language-section">
                                <h4><i class="fas fa-flag"></i> Ελληνικά (Greek)</h4>

                                <div class="form-group">
                                    <label for="booking_subject_el"><?= at('email_subject', 'Email Subject') ?></label>
                                    <input type="text" id="booking_subject_el" name="booking_confirmation_subject_el"
                                        value="<?= htmlspecialchars($emailTemplates['booking_confirmation']['el']['subject'] ?? $defaultTemplates['booking_confirmation']['el']['subject']) ?>"
                                        placeholder="<?= at('email_subject_placeholder', 'Enter email subject...') ?>">
                                    <small class="form-help"><?= at('available_placeholders', 'Available placeholders') ?>: {{BUSINESS_NAME}}</small>
                                </div>

                                <div class="form-group">
                                    <label for="booking_content_el"><?= at('email_content', 'Email Content') ?></label>
                                    <textarea id="booking_content_el" name="booking_confirmation_content_el" rows="15"
                                        placeholder="<?= at('email_content_placeholder', 'Enter email content...') ?>"><?= htmlspecialchars($emailTemplates['booking_confirmation']['el']['content'] ?? $defaultTemplates['booking_confirmation']['el']['content']) ?></textarea>
                                    <small class="form-help"><?= at('booking_placeholders', 'Available placeholders') ?>: {{BUSINESS_NAME}}, {{CUSTOMER_NAME}}, {{SERVICE_NAME}}, {{DATE}}, {{TIME}}, {{DURATION}}, {{EMPLOYEE_NAME}}, {{PRICE}}</small>
                                </div>
                            </div>

                            <!-- English Template -->
                            <div class="language-section">
                                <h4><i class="fas fa-flag"></i> English</h4>

                                <div class="form-group">
                                    <label for="booking_subject_en"><?= at('email_subject', 'Email Subject') ?></label>
                                    <input type="text" id="booking_subject_en" name="booking_confirmation_subject_en"
                                        value="<?= htmlspecialchars($emailTemplates['booking_confirmation']['en']['subject'] ?? $defaultTemplates['booking_confirmation']['en']['subject']) ?>"
                                        placeholder="<?= at('email_subject_placeholder', 'Enter email subject...') ?>">
                                    <small class="form-help"><?= at('available_placeholders', 'Available placeholders') ?>: {{BUSINESS_NAME}}</small>
                                </div>

                                <div class="form-group">
                                    <label for="booking_content_en"><?= at('email_content', 'Email Content') ?></label>
                                    <textarea id="booking_content_en" name="booking_confirmation_content_en" rows="15"
                                        placeholder="<?= at('email_content_placeholder', 'Enter email content...') ?>"><?= htmlspecialchars($emailTemplates['booking_confirmation']['en']['content'] ?? $defaultTemplates['booking_confirmation']['en']['content']) ?></textarea>
                                    <small class="form-help"><?= at('booking_placeholders', 'Available placeholders') ?>: {{BUSINESS_NAME}}, {{CUSTOMER_NAME}}, {{SERVICE_NAME}}, {{DATE}}, {{TIME}}, {{DURATION}}, {{EMPLOYEE_NAME}}, {{PRICE}}</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                <?= at('save_templates', 'Save Templates') ?>
                            </button>
                        </div>
                    </form>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
    .language-sections {
        display: grid;
        gap: 2rem;
    }

    .language-section {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 1.5rem;
    }

    .language-section h4 {
        margin: 0 0 1rem 0;
        color: #495057;
        font-size: 1.1rem;
    }

    .language-section h4 i {
        margin-right: 0.5rem;
        color: #6c757d;
    }

    .form-help {
        display: block;
        margin-top: 0.25rem;
        font-size: 0.875rem;
        color: #6c757d;
    }

    .tabs-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .tabs {
        display: flex;
        border-bottom: 1px solid #e9ecef;
        background: #f8f9fa;
    }

    .tab {
        padding: 1rem 1.5rem;
        text-decoration: none;
        color: #6c757d;
        border-bottom: 3px solid transparent;
        transition: all 0.3s ease;
    }

    .tab:hover {
        background: #e9ecef;
        color: #495057;
    }

    .tab.active {
        background: white;
        color: #007bff;
        border-bottom-color: #007bff;
    }

    .tab i {
        margin-right: 0.5rem;
    }

    .tab-content {
        padding: 2rem;
    }

    .settings-form textarea {
        min-height: 200px;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
    }

    .form-actions {
        margin-top: 2rem;
        padding-top: 1rem;
        border-top: 1px solid #e9ecef;
    }
</style>
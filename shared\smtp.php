<?php

/**
 * SMTP Email System
 * Handles email sending with tenant-specific configuration and templates
 */

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/tenant_manager.php';
require_once __DIR__ . '/EmailTemplate.php';

class SMTPMailer
{
    private string $host;
    private int $port;
    private string $username;
    private string $password;
    private string $fromEmail;
    private string $fromName;
    private string $security;
    private bool $enabled;

    /**
     * Initialize SMTP mailer with tenant configuration
     */
    public function __construct()
    {
        $this->loadSettings();
    }

    /**
     * Load SMTP settings from database with fallback to defaults
     */
    private function loadSettings(): void
    {
        try {
            $db = TenantManager::getDatabase();

            $this->enabled = (bool)$this->getSetting($db, 'smtp_enabled', '0');
            $this->host = $this->getSetting($db, 'smtp_host', '');
            $this->port = (int)$this->getSetting($db, 'smtp_port', '587');
            $this->security = $this->getSetting($db, 'smtp_security', 'tls');
            $this->username = $this->getSetting($db, 'smtp_username', '');
            $this->password = $this->getSetting($db, 'smtp_password', '');
            $this->fromEmail = $this->getSetting($db, 'business_email', '');
            $this->fromName = $this->getSetting($db, 'business_name', 'Booking System');

            // If SMTP is enabled but settings are incomplete, disable it
            if ($this->enabled && (empty($this->host) || empty($this->username) || empty($this->password))) {
                error_log("SMTP is enabled but configuration is incomplete. Disabling SMTP.");
                $this->enabled = false;
            }
        } catch (Exception $e) {
            error_log("Failed to load SMTP settings: " . $e->getMessage());
            // Fallback - disable SMTP and use PHP mail
            $this->enabled = false;
            $this->host = '';
            $this->port = 587;
            $this->security = 'tls';
            $this->username = '';
            $this->password = '';
            $this->fromEmail = '';
            $this->fromName = 'Booking System';
        }
    }

    /**
     * Get setting value from database with fallback
     */
    private function getSetting($db, string $key, string $default = ''): string
    {
        $setting = $db->fetchRow("SELECT value FROM settings WHERE key = :key", [':key' => $key]);
        return $setting ? $setting['value'] : $default;
    }

    /**
     * Send email using SMTP or fallback to PHP mail
     */
    public function send(string $to, string $subject, string $body, bool $isHTML = true): bool
    {
        if (!$this->enabled) {
            error_log("SMTP is disabled - using PHP mail() fallback for: $to");
            return $this->sendWithPhpMail($to, $subject, $body, $isHTML);
        }

        try {
            $socket = $this->connect();

            if (!$socket) {
                error_log("SMTP connection failed - using PHP mail() fallback for: $to");
                return $this->sendWithPhpMail($to, $subject, $body, $isHTML);
            }

            $this->authenticate($socket);
            $this->sendMessage($socket, $to, $subject, $body, $isHTML);
            $this->disconnect($socket);

            error_log("Email sent successfully via SMTP to: $to");
            return true;
        } catch (Exception $e) {
            error_log("SMTP Error: " . $e->getMessage() . " - using PHP mail() fallback for: $to");
            return $this->sendWithPhpMail($to, $subject, $body, $isHTML);
        }
    }

    private function connect()
    {
        // Use SSL context for SSL connections
        if ($this->security === 'ssl') {
            $context = stream_context_create([
                'ssl' => [
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                    'allow_self_signed' => true
                ]
            ]);
            $socket = stream_socket_client("ssl://{$this->host}:{$this->port}", $errno, $errstr, 30, STREAM_CLIENT_CONNECT, $context);
        } else {
            $socket = fsockopen($this->host, $this->port, $errno, $errstr, 30);
        }

        if (!$socket) {
            error_log("Failed to connect to SMTP server {$this->host}:{$this->port}: $errstr ($errno)");
            return false;
        }

        $this->getResponse($socket);

        // Start TLS if specified and not already using SSL
        if ($this->security === 'tls' && $this->security !== 'ssl') {
            fwrite($socket, "STARTTLS\r\n");
            $response = $this->getResponse($socket);

            if (!stream_socket_enable_crypto($socket, true, STREAM_CRYPTO_METHOD_TLS_CLIENT)) {
                error_log("Failed to enable TLS encryption");
                fclose($socket);
                return false;
            }
        }

        fwrite($socket, "EHLO " . ($_SERVER['HTTP_HOST'] ?? 'localhost') . "\r\n");
        $this->getResponse($socket);

        return $socket;
    }

    private function authenticate($socket): void
    {
        fwrite($socket, "AUTH LOGIN\r\n");
        $this->getResponse($socket);

        fwrite($socket, base64_encode($this->username) . "\r\n");
        $this->getResponse($socket);

        fwrite($socket, base64_encode($this->password) . "\r\n");
        $this->getResponse($socket);
    }

    private function sendMessage($socket, string $to, string $subject, string $body, bool $isHTML): void
    {
        fwrite($socket, "MAIL FROM: <{$this->fromEmail}>\r\n");
        $this->getResponse($socket);

        fwrite($socket, "RCPT TO: <$to>\r\n");
        $this->getResponse($socket);

        fwrite($socket, "DATA\r\n");
        $this->getResponse($socket);

        $headers = $this->buildHeaders($to, $subject, $isHTML);
        fwrite($socket, $headers . "\r\n" . $body . "\r\n.\r\n");
        $this->getResponse($socket);
    }

    private function buildHeaders(string $to, string $subject, bool $isHTML): string
    {
        $headers = [];
        $headers[] = "From: {$this->fromName} <{$this->fromEmail}>";
        $headers[] = "To: $to";
        $headers[] = "Subject: $subject";
        $headers[] = "Date: " . date('r');
        $headers[] = "Message-ID: <" . uniqid() . "@{$_SERVER['HTTP_HOST']}>";
        $headers[] = "MIME-Version: 1.0";

        if ($isHTML) {
            $headers[] = "Content-Type: text/html; charset=UTF-8";
        } else {
            $headers[] = "Content-Type: text/plain; charset=UTF-8";
        }

        $headers[] = "Content-Transfer-Encoding: 8bit";

        return implode("\r\n", $headers);
    }

    private function getResponse($socket): string
    {
        $response = '';
        while ($line = fgets($socket, 515)) {
            $response .= $line;
            if (substr($line, 3, 1) == ' ') {
                break;
            }
        }
        return $response;
    }

    private function disconnect($socket): void
    {
        fwrite($socket, "QUIT\r\n");
        $this->getResponse($socket);
        fclose($socket);
    }

    public function sendVerificationCode(string $to, string $code, string $businessName = '', string $language = 'el'): bool
    {
        $businessName = $businessName ?: 'Booking System';
        $emailData = EmailTemplate::generateVerificationEmail($code, $businessName, $language);

        $subject = $emailData['subject'];
        $body = $emailData['body'];

        $success = $this->send($to, $subject, $body, true);

        // Log the email
        $this->logEmail([
            'customer_email' => $to,
            'subject' => $subject,
            'content' => $body,
            'email_type' => 'verification',
            'language' => $language,
            'status' => $success ? 'sent' : 'failed'
        ]);

        return $success;
    }

    public function sendBookingConfirmation(string $to, array $bookingData, string $language = 'el'): bool
    {
        $emailData = EmailTemplate::generateBookingConfirmation($bookingData, $language);

        $subject = $emailData['subject'];
        $body = $emailData['body'];

        $success = $this->send($to, $subject, $body, true);

        // Log the email
        $this->logEmail([
            'customer_email' => $to,
            'customer_name' => $bookingData['customer_name'] ?? '',
            'subject' => $subject,
            'content' => $body,
            'email_type' => 'booking_confirmation',
            'language' => $language,
            'status' => $success ? 'sent' : 'failed'
        ]);

        return $success;
    }

    /**
     * Send admin notification for new reservation
     */
    public function sendAdminReservationNotification(array $reservationData): bool
    {
        try {
            $db = TenantManager::getDatabase();

            // Get admin email from business settings
            $adminEmail = $this->getSetting($db, 'business_email', '');
            if (empty($adminEmail)) {
                error_log("Admin notification skipped: No business email configured");
                return false;
            }

            // Check if admin notifications are enabled
            $adminNotificationsEnabled = $this->getSetting($db, 'admin_notifications_enabled', '1') === '1';
            if (!$adminNotificationsEnabled) {
                error_log("Admin notification skipped: Admin notifications disabled");
                return false;
            }

            $businessName = $this->getSetting($db, 'business_name', 'Booking System');
            $defaultLanguage = $this->getSetting($db, 'default_language', 'el');

            // Use EmailTemplate to generate admin notification email
            require_once __DIR__ . '/EmailTemplate.php';
            $emailData = EmailTemplate::generateAdminReservationNotification($reservationData, $businessName, $defaultLanguage);

            $success = $this->send($adminEmail, $emailData['subject'], $emailData['body'], true);

            // Log the email
            $this->logEmail([
                'customer_email' => $adminEmail,
                'subject' => $emailData['subject'],
                'content' => $emailData['body'],
                'email_type' => 'admin_notification',
                'language' => $defaultLanguage,
                'status' => $success ? 'sent' : 'failed'
            ]);

            return $success;
        } catch (Exception $e) {
            error_log("Failed to send admin notification: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send custom email (for admin manual sends)
     */
    public function sendCustomEmail(string $to, string $subject, string $content, string $customerName = '', ?string $customerId = null): bool
    {
        // Get business name from settings
        $db = TenantManager::getDatabase();
        $businessName = $this->getSetting($db, 'business_name', 'Booking System');
        $defaultLanguage = $this->getSetting($db, 'default_language', 'el');

        // Use EmailTemplate to generate consistent email structure
        require_once __DIR__ . '/EmailTemplate.php';
        $emailData = EmailTemplate::generateCustomEmail($subject, $content, $businessName, $customerName, $defaultLanguage);

        $success = $this->send($to, $emailData['subject'], $emailData['body'], true);

        // Log the email
        $this->logEmail([
            'customer_id' => $customerId,
            'customer_email' => $to,
            'customer_name' => $customerName,
            'subject' => $emailData['subject'],
            'content' => $emailData['body'],
            'email_type' => 'custom',
            'language' => $defaultLanguage,
            'status' => $success ? 'sent' : 'failed'
        ]);

        return $success;
    }

    /**
     * Log email to database
     */
    private function logEmail(array $emailData): void
    {
        try {
            require_once __DIR__ . '/EmailLogger.php';
            require_once __DIR__ . '/tenant_manager.php';

            $db = TenantManager::getDatabase();
            $logger = new EmailLogger($db);
            $logger->logEmail($emailData);
        } catch (Exception $e) {
            error_log("Failed to log email: " . $e->getMessage());
            // Don't throw exception - email logging shouldn't break email sending
        }
    }

    /**
     * Send email using PHP mail() function as fallback
     */
    private function sendWithPhpMail(string $to, string $subject, string $body, bool $isHTML = true): bool
    {
        try {
            // Set headers for HTML email
            $headers = [];
            if ($isHTML) {
                $headers[] = 'MIME-Version: 1.0';
                $headers[] = 'Content-type: text/html; charset=UTF-8';
            }

            // Use business email if available, otherwise fallback
            $fromEmail = !empty($this->fromEmail) ? $this->fromEmail : 'noreply@' . ($_SERVER['HTTP_HOST'] ?? 'localhost');
            $fromName = !empty($this->fromName) ? $this->fromName : 'Booking System';

            $headers[] = "From: $fromName <$fromEmail>";
            $headers[] = "Reply-To: $fromEmail";
            $headers[] = 'X-Mailer: GK Radevou Booking System';

            $headerString = implode("\r\n", $headers);

            // Send email using PHP mail()
            $result = mail($to, $subject, $body, $headerString);

            if ($result) {
                error_log("Email sent successfully using PHP mail() to: $to (from: $fromEmail)");
                return true;
            } else {
                error_log("Failed to send email using PHP mail() to: $to");
                // Fallback to file saving if mail() fails
                return $this->saveEmailToFile($to, $subject, $body, $isHTML);
            }
        } catch (Exception $e) {
            error_log("PHP mail() error: " . $e->getMessage());
            // Fallback to file saving if mail() fails
            return $this->saveEmailToFile($to, $subject, $body, $isHTML);
        }
    }

    /**
     * Save email to file for development mode (last resort fallback)
     */
    private function saveEmailToFile(string $to, string $subject, string $body, bool $isHTML = true): bool
    {
        try {
            $emailsDir = __DIR__ . '/../temp/emails';
            if (!is_dir($emailsDir)) {
                mkdir($emailsDir, 0755, true);
            }

            $timestamp = date('Y-m-d_H-i-s');
            $filename = $emailsDir . "/email_{$timestamp}_" . preg_replace('/[^a-zA-Z0-9]/', '_', $to) . '.html';

            $content = "
<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>Email: {$subject}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .email-header { background: #f0f0f0; padding: 15px; margin-bottom: 20px; border-radius: 5px; }
        .email-body { border: 1px solid #ddd; padding: 20px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class='email-header'>
        <h2>📧 Development Email</h2>
        <p><strong>To:</strong> {$to}</p>
        <p><strong>Subject:</strong> {$subject}</p>
        <p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>
        <p><strong>Type:</strong> " . ($isHTML ? 'HTML' : 'Text') . "</p>
    </div>
    <div class='email-body'>
        {$body}
    </div>
</body>
</html>";

            $result = file_put_contents($filename, $content);

            if ($result !== false) {
                error_log("Email saved to file: {$filename}");
                return true;
            } else {
                error_log("Failed to save email to file: {$filename}");
                return false;
            }
        } catch (Exception $e) {
            error_log("Error saving email to file: " . $e->getMessage());
            return false;
        }
    }
}

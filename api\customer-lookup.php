<?php

/**
 * Customer Lookup API
 * Lookup existing customers by email for verification-only booking mode
 */

// Basic error handling
error_reporting(0);
ini_set('display_errors', 0);

require_once __DIR__ . '/../shared/config.php';
require_once __DIR__ . '/../shared/database.php';
require_once __DIR__ . '/../shared/tenant_manager.php';
require_once __DIR__ . '/../shared/functions.php';

// Set JSON response headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Response functions are already defined in shared/functions.php

// Validate request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    errorResponse('Method not allowed', 405);
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    errorResponse('Invalid JSON input');
}

$email = trim($input['email'] ?? '');

if (empty($email)) {
    errorResponse('Email is required');
}

// Validate email format
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    errorResponse('Invalid email format');
}

try {
    // Initialize tenant context first
    TenantManager::init();

    $db = TenantManager::getDatabase();

    if (!$db) {
        errorResponse('Database connection failed');
    }

    // Look up customer by email
    $customer = $db->fetchRow(
        "SELECT id, name, email, phone, language FROM customers WHERE email = :email LIMIT 1",
        [':email' => $email]
    );

    if ($customer) {
        // Customer found - return customer data
        successResponse([
            'found' => true,
            'customer' => [
                'id' => (int)$customer['id'],
                'name' => $customer['name'] ?? '',
                'email' => $customer['email'] ?? '',
                'phone' => $customer['phone'] ?? '',
                'language' => $customer['language'] ?? 'el'
            ]
        ]);
    } else {
        // Customer not found
        successResponse([
            'found' => false,
            'customer' => null
        ]);
    }
} catch (Exception $e) {
    logActivity("Customer lookup failed: " . $e->getMessage(), 'error');
    errorResponse('Lookup failed: ' . $e->getMessage(), 500);
}

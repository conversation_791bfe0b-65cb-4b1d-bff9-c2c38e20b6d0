<?php

/**
 * Email Logs View
 * Display all emails sent from the system
 */

require_once __DIR__ . '/../../shared/EmailLogger.php';

// Get filters from request
$filters = [
    'customer_email' => $_GET['customer_email'] ?? '',
    'email_type' => $_GET['email_type'] ?? '',
    'language' => $_GET['language'] ?? '',
    'date_from' => $_GET['date_from'] ?? '',
    'date_to' => $_GET['date_to'] ?? ''
];

$page = max(1, intval($_GET['page'] ?? 1));
$limit = 25;

// Get email logs
$db = TenantManager::getDatabase();
$logger = new EmailLogger($db);
$result = $logger->getEmailLogs($filters, $page, $limit);
$stats = $logger->getEmailStats();

?>

<div class="page-header">
    <div class="page-title">
        <h1><i class="fas fa-envelope-open-text"></i> <?= at('email_logs', 'Email Logs') ?></h1>
        <p class="page-subtitle"><?= at('email_logs_subtitle', 'View all emails sent from the system') ?></p>
    </div>

    <div class="page-actions">
        <button class="btn btn-secondary" onclick="showEmailStats()">
            <i class="fas fa-chart-bar"></i> <?= at('statistics', 'Statistics') ?>
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-envelope"></i>
        </div>
        <div class="stat-content">
            <div class="stat-number"><?= number_format($stats['total_emails']) ?></div>
            <div class="stat-label"><?= at('total_emails', 'Total Emails') ?></div>
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-calendar-day"></i>
        </div>
        <div class="stat-content">
            <div class="stat-number"><?= number_format($stats['today_emails']) ?></div>
            <div class="stat-label"><?= at('today_emails', 'Today\'s Emails') ?></div>
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="stat-content">
            <div class="stat-number"><?= count($stats['by_type']) ?></div>
            <div class="stat-label"><?= at('email_types', 'Email Types') ?></div>
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-globe"></i>
        </div>
        <div class="stat-content">
            <div class="stat-number"><?= count($stats['by_language']) ?></div>
            <div class="stat-label"><?= at('languages_used', 'Languages Used') ?></div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="filters-section">
    <form method="GET" class="filters-form">
        <input type="hidden" name="page" value="email-logs">

        <div class="filter-group">
            <label for="customer_email"><?= at('customer_email', 'Customer Email') ?></label>
            <input type="email" id="customer_email" name="customer_email"
                value="<?= htmlspecialchars($filters['customer_email']) ?>"
                placeholder="<?= at('search_by_email', 'Search by email...') ?>">
        </div>

        <div class="filter-group">
            <label for="email_type"><?= at('email_type', 'Email Type') ?></label>
            <select id="email_type" name="email_type">
                <option value=""><?= at('all_types', 'All Types') ?></option>
                <option value="verification" <?= $filters['email_type'] === 'verification' ? 'selected' : '' ?>>
                    <?= at('verification', 'Verification') ?>
                </option>
                <option value="booking_confirmation" <?= $filters['email_type'] === 'booking_confirmation' ? 'selected' : '' ?>>
                    <?= at('booking_confirmation', 'Booking Confirmation') ?>
                </option>
                <option value="custom" <?= $filters['email_type'] === 'custom' ? 'selected' : '' ?>>
                    <?= at('custom', 'Custom') ?>
                </option>
            </select>
        </div>

        <div class="filter-group">
            <label for="language"><?= at('language', 'Language') ?></label>
            <select id="language" name="language">
                <option value=""><?= at('all_languages', 'All Languages') ?></option>
                <option value="el" <?= $filters['language'] === 'el' ? 'selected' : '' ?>>Ελληνικά</option>
                <option value="en" <?= $filters['language'] === 'en' ? 'selected' : '' ?>>English</option>
            </select>
        </div>

        <div class="filter-group">
            <label for="date_from"><?= at('date_from', 'Date From') ?></label>
            <input type="date" id="date_from" name="date_from"
                value="<?= htmlspecialchars($filters['date_from']) ?>">
        </div>

        <div class="filter-group">
            <label for="date_to"><?= at('date_to', 'Date To') ?></label>
            <input type="date" id="date_to" name="date_to"
                value="<?= htmlspecialchars($filters['date_to']) ?>">
        </div>

        <div class="filter-actions">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search"></i> <?= at('filter', 'Filter') ?>
            </button>
            <a href="?page=email-logs" class="btn btn-secondary">
                <i class="fas fa-times"></i> <?= at('clear', 'Clear') ?>
            </a>
        </div>
    </form>
</div>

<!-- Email Logs Table -->
<div class="table-container">
    <table class="data-table">
        <thead>
            <tr>
                <th><?= at('sent_at', 'Sent At') ?></th>
                <th><?= at('customer', 'Customer') ?></th>
                <th><?= at('subject', 'Subject') ?></th>
                <th><?= at('type', 'Type') ?></th>
                <th><?= at('language', 'Language') ?></th>
                <th><?= at('status', 'Status') ?></th>
                <th><?= at('actions', 'Actions') ?></th>
            </tr>
        </thead>
        <tbody>
            <?php if (empty($result['logs'])): ?>
                <tr>
                    <td colspan="7" class="no-data">
                        <i class="fas fa-inbox"></i>
                        <p><?= at('no_email_logs', 'No email logs found') ?></p>
                    </td>
                </tr>
            <?php else: ?>
                <?php foreach ($result['logs'] as $log): ?>
                    <tr class="clickable-row" data-email-id="<?= htmlspecialchars($log['id'], ENT_QUOTES) ?>">
                        <td>
                            <div class="datetime-info">
                                <div class="date"><?= date('M j, Y', strtotime($log['sent_at'])) ?></div>
                                <div class="time"><?= date('H:i', strtotime($log['sent_at'])) ?></div>
                            </div>
                        </td>
                        <td>
                            <div class="customer-info">
                                <div class="name"><?= htmlspecialchars($log['customer_name'] ?: $log['customer_full_name'] ?: 'Unknown') ?></div>
                                <div class="email"><?= htmlspecialchars($log['customer_email']) ?></div>
                            </div>
                        </td>
                        <td>
                            <div class="subject-preview">
                                <?= htmlspecialchars(mb_substr($log['subject'], 0, 50)) ?>
                                <?= mb_strlen($log['subject']) > 50 ? '...' : '' ?>
                            </div>
                        </td>
                        <td>
                            <span class="badge badge-<?= $log['email_type'] ?>">
                                <?= ucfirst(str_replace('_', ' ', $log['email_type'])) ?>
                            </span>
                        </td>
                        <td>
                            <span class="language-badge">
                                <?= strtoupper($log['language']) ?>
                            </span>
                        </td>
                        <td>
                            <span class="status-badge status-<?= $log['status'] ?>">
                                <i class="fas fa-<?= $log['status'] === 'sent' ? 'check' : 'times' ?>"></i>
                                <?= ucfirst($log['status']) ?>
                            </span>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-sm btn-primary" data-action="view-email" data-email-id="<?= htmlspecialchars($log['id'], ENT_QUOTES) ?>">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <?php if ($log['customer_id']): ?>
                                    <button class="btn btn-sm btn-secondary" data-action="view-customer" data-customer-id="<?= htmlspecialchars($log['customer_id'], ENT_QUOTES) ?>">
                                        <i class="fas fa-user"></i>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<!-- Pagination -->
<?php if ($result['total_pages'] > 1): ?>
    <div class="pagination-container">
        <div class="pagination-info">
            <?= at('showing', 'Showing') ?> <?= (($page - 1) * $limit) + 1 ?> - <?= min($page * $limit, $result['total']) ?>
            <?= at('of', 'of') ?> <?= $result['total'] ?> <?= at('emails', 'emails') ?>
        </div>

        <div class="pagination">
            <?php if ($page > 1): ?>
                <a href="?page=email-logs&<?= http_build_query(array_merge($filters, ['page' => $page - 1])) ?>" class="pagination-btn">
                    <i class="fas fa-chevron-left"></i>
                </a>
            <?php endif; ?>

            <?php for ($i = max(1, $page - 2); $i <= min($result['total_pages'], $page + 2); $i++): ?>
                <a href="?page=email-logs&<?= http_build_query(array_merge($filters, ['page' => $i])) ?>"
                    class="pagination-btn <?= $i === $page ? 'active' : '' ?>">
                    <?= $i ?>
                </a>
            <?php endfor; ?>

            <?php if ($page < $result['total_pages']): ?>
                <a href="?page=email-logs&<?= http_build_query(array_merge($filters, ['page' => $page + 1])) ?>" class="pagination-btn">
                    <i class="fas fa-chevron-right"></i>
                </a>
            <?php endif; ?>
        </div>
    </div>
<?php endif; ?>

<!-- Email View Modal -->
<div id="emailModal" class="modal">
    <div class="modal-content modal-large">
        <div class="modal-header">
            <h3><i class="fas fa-envelope-open"></i> <?= at('email_details', 'Email Details') ?></h3>
            <button class="modal-close" onclick="closeEmailModal()">&times;</button>
        </div>
        <div class="modal-body" id="emailModalContent">
            <!-- Email content will be loaded here -->
        </div>
    </div>
</div>

<script>
    function viewEmailLog(logId) {
        showLoadingOverlay('Loading email details...');

        fetch(`/store-admin/controllers/ajax.php`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=get_email_log&log_id=${logId}`
            })
            .then(response => response.json())
            .then(data => {
                hideLoadingOverlay();
                if (data.success) {
                    showEmailModal(data.email);
                } else {
                    showNotification('Failed to load email details', 'error');
                }
            })
            .catch(error => {
                hideLoadingOverlay();
                showNotification('Error loading email details', 'error');
            });
    }

    function showEmailModal(email) {
        const modal = document.getElementById('emailModal');
        const content = document.getElementById('emailModalContent');

        content.innerHTML = `
        <div class="email-details">
            <div class="email-header">
                <div class="email-meta">
                    <div class="meta-row">
                        <label>To:</label>
                        <span>${email.customer_email}</span>
                    </div>
                    <div class="meta-row">
                        <label>Customer:</label>
                        <span>${email.customer_name || email.customer_full_name || 'Unknown'}</span>
                    </div>
                    <div class="meta-row">
                        <label>Subject:</label>
                        <span>${email.subject}</span>
                    </div>
                    <div class="meta-row">
                        <label>Type:</label>
                        <span class="badge badge-${email.email_type}">${email.email_type.replace('_', ' ')}</span>
                    </div>
                    <div class="meta-row">
                        <label>Language:</label>
                        <span class="language-badge">${email.language.toUpperCase()}</span>
                    </div>
                    <div class="meta-row">
                        <label>Sent:</label>
                        <span>${new Date(email.sent_at).toLocaleString()}</span>
                    </div>
                    <div class="meta-row">
                        <label>Status:</label>
                        <span class="status-badge status-${email.status}">${email.status}</span>
                    </div>
                </div>
            </div>
            <div class="email-content">
                <h4>Email Content:</h4>
                <div class="email-body">
                    ${email.content}
                </div>
            </div>
        </div>
    `;

        modal.style.display = 'flex';
        modal.classList.add('show');
    }

    function closeEmailModal() {
        const modal = document.getElementById('emailModal');
        modal.style.display = 'none';
        modal.classList.remove('show');
    }

    function showEmailStats() {
        // Implementation for showing detailed statistics
        showNotification('Statistics feature coming soon', 'info');
    }

    // Add click event listeners to email log rows and action buttons
    document.addEventListener('DOMContentLoaded', function() {
        // Handle row clicks
        const rows = document.querySelectorAll('.clickable-row[data-email-id]');
        rows.forEach(row => {
            row.addEventListener('click', function() {
                const emailId = this.getAttribute('data-email-id');
                viewEmailLog(emailId);
            });
        });

        // Handle action button clicks
        const actionButtons = document.querySelectorAll('[data-action]');
        actionButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation(); // Prevent row click
                const action = this.getAttribute('data-action');

                if (action === 'view-email') {
                    const emailId = this.getAttribute('data-email-id');
                    viewEmailLog(emailId);
                } else if (action === 'view-customer') {
                    const customerId = this.getAttribute('data-customer-id');
                    viewCustomer(customerId);
                }
            });
        });
    });

    // Close modal when clicking outside
    window.onclick = function(event) {
        const modal = document.getElementById('emailModal');
        if (event.target === modal) {
            closeEmailModal();
        }
    }
</script>
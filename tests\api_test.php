<?php
/**
 * API Test Script
 * Quick tests for the new search API and settings
 */

header('Content-Type: application/json');

// Test the search API
if (isset($_GET['test']) && $_GET['test'] === 'search') {
    $query = $_GET['q'] ?? 'test';
    
    // Make internal request to search API
    $searchUrl = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/../api/search.php?q=" . urlencode($query);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'timeout' => 10
        ]
    ]);
    
    $response = file_get_contents($searchUrl, false, $context);
    
    if ($response === false) {
        echo json_encode([
            'success' => false,
            'error' => 'Failed to connect to search API',
            'url' => $searchUrl
        ]);
    } else {
        $data = json_decode($response, true);
        echo json_encode([
            'success' => true,
            'api_response' => $data,
            'test_query' => $query,
            'url_tested' => $searchUrl
        ]);
    }
    exit;
}

// Test settings API
if (isset($_GET['test']) && $_GET['test'] === 'settings') {
    $settingsUrl = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/../api/settings.php";
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'timeout' => 10
        ]
    ]);
    
    $response = file_get_contents($settingsUrl, false, $context);
    
    if ($response === false) {
        echo json_encode([
            'success' => false,
            'error' => 'Failed to connect to settings API',
            'url' => $settingsUrl
        ]);
    } else {
        $data = json_decode($response, true);
        echo json_encode([
            'success' => true,
            'api_response' => $data,
            'url_tested' => $settingsUrl,
            'relevant_settings' => [
                'notification_emails' => $data['notification_emails'] ?? 'not found',
                'require_phone' => $data['require_phone'] ?? 'not found'
            ]
        ]);
    }
    exit;
}

// Default response
echo json_encode([
    'success' => false,
    'error' => 'No test specified',
    'available_tests' => [
        'search' => '?test=search&q=your_query',
        'settings' => '?test=settings'
    ]
]);
?>

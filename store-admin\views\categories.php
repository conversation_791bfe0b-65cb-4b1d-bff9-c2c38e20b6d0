<?php

/**
 * Categories View
 * Manage service categories
 */

// Get search and pagination parameters
$searchParams = AdminHelpers::getSearchParams();
$search = $searchParams['search'];
$pageNum = $searchParams['page_num'];
$perPage = $searchParams['per_page'];

// Build search query - search by name, English name, description, and icon
$searchWhere = AdminHelpers::buildSearchWhere($search, ['name', 'name_en', 'description', 'icon']);

// Get total count for pagination first
$countSql = "SELECT COUNT(*) as total FROM categories {$searchWhere['where']}";
$totalCount = $db->fetchRow($countSql, $searchWhere['params'])['total'];

// Create pagination
$pagination = new Pagination($totalCount, $perPage, $pageNum);

// Get categories with proper SQL pagination
$sql = "SELECT * FROM categories {$searchWhere['where']} ORDER BY sort_order ASC, name ASC LIMIT {$perPage} OFFSET {$pagination->getOffset()}";
$paginatedCategories = $db->fetchAll($sql, $searchWhere['params']);



// Add services to each category
foreach ($paginatedCategories as &$category) {
    $category['services'] = $db->fetchAll(
        "SELECT id, name, price, is_active FROM services WHERE category_id = :category_id ORDER BY name ASC",
        [':category_id' => $category['id']]
    );
}
unset($category); // Important: unset the reference to prevent issues with subsequent foreach loops

// Force browser cache refresh
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

?>



<!-- Main Toolbar -->
<div class="main-toolbar">
    <div class="toolbar-left">
        <a href="/store-admin/?page=add-category" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add Category
        </a>
        <button class="btn btn-secondary" onclick="reorderCategories()">
            <i class="fas fa-sort"></i> Reorder
        </button>
        <div class="view-toggle">
            <button class="active" data-view="cards">
                <i class="fas fa-th-large"></i> Cards
            </button>
            <button data-view="table">
                <i class="fas fa-list"></i> Table
            </button>
        </div>
    </div>
    <div class="toolbar-right">
        <div class="dropdown">
            <button class="btn btn-primary dropdown-toggle" type="button" data-toggle="dropdown">
                <i class="fas fa-download"></i> Export
            </button>
            <div class="dropdown-menu">
                <a class="dropdown-item" href="#" onclick="exportCategories('csv')">
                    <i class="fas fa-file-csv"></i> Export as CSV
                </a>
                <a class="dropdown-item" href="#" onclick="exportCategories('xlsx')">
                    <i class="fas fa-file-excel"></i> Export as XLSX
                </a>
            </div>
        </div>
        <div class="dropdown">
            <button class="btn btn-secondary dropdown-toggle" type="button" data-toggle="dropdown">
                <i class="fas fa-upload"></i> Import
            </button>
            <div class="dropdown-menu">
                <a class="dropdown-item" href="#" onclick="downloadCategoriesTemplate()">
                    <i class="fas fa-download"></i> Download Template
                </a>
                <a class="dropdown-item" href="#" onclick="importCategories()">
                    <i class="fas fa-upload"></i> Import XLSX
                </a>
            </div>
        </div>
        <button class="btn btn-secondary" onclick="toggleFilters()" id="filter-toggle-btn">
            <i class="fas fa-filter"></i> Filters
        </button>
        <div class="search-bar">
            <input type="text" placeholder="Search categories, descriptions..." value="<?php echo htmlspecialchars($search); ?>" id="category-search">
            <button type="button" class="search-btn" onclick="performSearch(document.getElementById('category-search').value)">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>
</div>

<!-- Filters Section -->
<div class="filters-section" id="filters-section" style="display: none;">
    <div class="filters-header">
        <h3><i class="fas fa-filter"></i> Filters</h3>
        <button class="btn btn-outline btn-sm" onclick="clearFilters()">
            <i class="fas fa-times"></i> Clear All
        </button>
    </div>
    <div class="filters-content">
        <div class="filter-group">
            <label class="filter-label">Sort:</label>
            <select class="form-control" id="sort-filter">
                <option value="sort_order">Sort Order</option>
                <option value="name">Name A-Z</option>
                <option value="services_count">Service Count</option>
                <option value="created_at">Date Created</option>
            </select>
        </div>
        <div class="filter-group">
            <label class="filter-label">Services:</label>
            <select class="form-control" id="services-filter">
                <option value="">All Categories</option>
                <option value="with-services">With Services</option>
                <option value="empty">Empty Categories</option>
            </select>
        </div>
        <div class="filter-group">
            <label class="filter-label">Status:</label>
            <select class="form-control" id="status-filter">
                <option value="">All Categories</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
            </select>
        </div>
        <div class="filter-group">
            <label class="filter-label">Color:</label>
            <select class="form-control" id="color-filter">
                <option value="">All Colors</option>
                <option value="blue">Blue</option>
                <option value="green">Green</option>
                <option value="red">Red</option>
                <option value="purple">Purple</option>
                <option value="orange">Orange</option>
            </select>
        </div>
    </div>
</div>



<!-- Enhanced Categories Grid -->
<div class="entity-grid" id="categories-grid">
    <?php if (empty($paginatedCategories)): ?>
        <div class="empty-state">
            <i class="fas fa-tags fa-2x text-muted"></i>
            <h3>No categories found</h3>
            <p>Start by adding your first category or adjust your search criteria.</p>
            <button class="btn btn-primary btn-lg" onclick="addCategory()">
                <i class="fas fa-plus"></i> Add First Category
            </button>
        </div>
    <?php else: ?>
        <?php
        // Include the entity card component
        require_once __DIR__ . '/components/entity-card.php';

        foreach ($paginatedCategories as $categoryIndex => $category):
            // Prepare category data for the card component
            $serviceCount = count($category['services'] ?? []);
            $categoryColor = $category['color'] ?: '#' . substr(md5($category['name']), 0, 6);

            // Prepare category data for card
            $categoryData = [
                'id' => $category['id'],
                'name' => $category['name'],
                'name_en' => $category['name_en'] ?? '',
                'description' => $category['description'] ?? '',
                'color' => $categoryColor,
                'icon' => $category['icon'] ?? 'fas fa-tag',
                'active' => $category['is_active'] ?? true,
                'services_count' => $serviceCount,
                'sort_order' => $category['sort_order'] ?? 0,
                'services' => $category['services'] ?? [],
                'created_at' => $category['created_at'] ?? null
            ];

            // Render the category card
            echo renderEntityCard($categoryData, 'category', [
                'show_checkbox' => false,
                'show_actions' => false
            ]);
        endforeach; ?>

        <!-- Add Category Card -->
        <div class="entity-card add-card" onclick="window.location.href='/store-admin/?page=add-category'">
            <div class="card__body">
                <div class="add-card-content">
                    <i class="fas fa-plus fa-2x" style="color: var(--gray-400); margin-bottom: 16px;"></i>
                    <h3 style="color: var(--gray-600); font-size: var(--text-lg);">Add New Category</h3>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Pagination -->
<?php if ($pagination->getTotalPages() > 1): ?>
    <div class="pagination-container">
        <?php echo $pagination->render(); ?>
    </div>
<?php endif; ?>

<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script>
    // Export categories function
    function exportCategories(format = 'csv') {
        if (format === 'xlsx') {
            exportCategoriesXLSX();
        } else {
            exportCategoriesCSV();
        }
    }

    function exportCategoriesCSV() {
        const urlParams = new URLSearchParams(window.location.search);
        const params = new URLSearchParams();

        // Add current filters to export
        if (urlParams.get('search')) params.append('search', urlParams.get('search'));
        if (urlParams.get('sort')) params.append('sort', urlParams.get('sort'));
        if (urlParams.get('status')) params.append('status', urlParams.get('status'));

        params.append('action', 'export_categories');
        params.append('format', 'csv');

        window.open('/store-admin/controllers/export.php?' + params.toString(), '_blank');
    }

    async function exportCategoriesXLSX() {
        try {
            // Fetch data from server
            const urlParams = new URLSearchParams(window.location.search);
            const params = new URLSearchParams();

            if (urlParams.get('search')) params.append('search', urlParams.get('search'));
            if (urlParams.get('sort')) params.append('sort', urlParams.get('sort'));
            if (urlParams.get('status')) params.append('status', urlParams.get('status'));

            params.append('action', 'get_categories_data');

            const response = await fetch('/store-admin/controllers/ajax.php?' + params.toString());
            const result = await response.json();

            if (!result.success) {
                throw new Error(result.error || 'Failed to fetch data');
            }

            // Create workbook
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(result.data);

            // Add worksheet to workbook
            XLSX.utils.book_append_sheet(wb, ws, 'Categories');

            // Download file
            XLSX.writeFile(wb, `categories_export_${new Date().toISOString().split('T')[0]}.xlsx`);

        } catch (error) {
            console.error('Export error:', error);
            alert('Export failed: ' + error.message);
        }
    }

    function downloadCategoriesTemplate() {
        const templateData = [{
                name: 'Hair Services',
                description: 'Professional hair cutting and styling services',
                icon: 'fas fa-cut',
                color: 'blue',
                is_active: 1,
                sort_order: 1
            },
            {
                name: 'Beauty Services',
                description: 'Facial treatments and beauty services',
                icon: 'fas fa-spa',
                color: 'green',
                is_active: 1,
                sort_order: 2
            }
        ];

        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(templateData);

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, 'Categories Template');

        // Download template
        XLSX.writeFile(wb, 'categories_import_template.xlsx');
    }

    function importCategories() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.xlsx,.xls';
        input.onchange = function(e) {
            const file = e.target.files[0];
            if (file) {
                processCategoriesImport(file);
            }
        };
        input.click();
    }

    async function processCategoriesImport(file) {
        try {
            const data = await file.arrayBuffer();
            const workbook = XLSX.read(data);
            const worksheet = workbook.Sheets[workbook.SheetNames[0]];
            const jsonData = XLSX.utils.sheet_to_json(worksheet);

            if (jsonData.length === 0) {
                alert('No data found in the file');
                return;
            }

            // Send data to server
            const formData = new FormData();
            formData.append('action', 'import_categories');
            formData.append('data', JSON.stringify(jsonData));

            const response = await fetch('/store-admin/controllers/ajax.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                alert(`Successfully imported ${result.imported} categories`);
                window.location.reload();
            } else {
                alert('Import failed: ' + (result.error || 'Unknown error'));
            }

        } catch (error) {
            console.error('Import error:', error);
            alert('Import failed: ' + error.message);
        }
    }
</script>
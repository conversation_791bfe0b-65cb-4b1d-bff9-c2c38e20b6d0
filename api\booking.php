<?php

/**
 * Booking API Endpoint
 * Handles booking creation with availability checking and verification
 */

// Basic error handling
error_reporting(0);
ini_set('display_errors', 0);

// Set headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// Include required dependencies
require_once __DIR__ . '/../shared/config.php';
require_once __DIR__ . '/../shared/database.php';
require_once __DIR__ . '/../shared/tenant_manager.php';
require_once __DIR__ . '/../shared/functions.php';
require_once __DIR__ . '/../shared/availability_checker.php';
require_once __DIR__ . '/../shared/verification.php';
require_once __DIR__ . '/../shared/error_messages.php';
require_once __DIR__ . '/../shared/smtp.php';

// Initialize systems
Config::init();
TenantManager::init();

// Simple response function
function sendResponse($success, $data = null, $message = 'Success')
{
    $response = [
        'success' => $success,
        'message' => $message,
        'data' => $data
    ];
    echo json_encode($response);
    exit;
}

// Validate request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, null, 'Method not allowed');
}

// Parse JSON input
$rawInput = file_get_contents('php://input');
$input = json_decode($rawInput, true);

if (!$input) {
    sendResponse(false, null, 'Invalid JSON input');
}

try {
    $serviceId = $input['service_id'] ?? null;
    $employeeId = $input['employee_id'] ?? null;
    $date = $input['date'] ?? null;
    $time = $input['time'] ?? null;
    $customer = $input['customer'] ?? null;
    $notes = $input['notes'] ?? '';

    // Extract customer details
    $customerName = $customer['name'] ?? $input['customer_name'] ?? null;
    $customerEmail = $customer['email'] ?? $input['customer_email'] ?? null;
    $customerPhone = $customer['phone'] ?? $input['customer_phone'] ?? null;
    $customerNotes = $customer['notes'] ?? $notes;

    // Get customer's preferred language from request data or default to Greek
    $customerLanguage = $input['language'] ?? 'el';

    // Debug logging
    error_log("🌐 Booking API Language Debug: " . json_encode([
        'received_language' => $input['language'] ?? 'not_set',
        'final_language' => $customerLanguage,
        'full_input' => array_keys($input),
        'timestamp' => date('Y-m-d H:i:s')
    ]));

    // Validate required fields
    if (!$serviceId || !$date || !$time || !$customerName || !$customerEmail) {
        sendResponse(false, null, 'Missing required fields');
    }

    // Validate email
    if (!validateEmail($customerEmail)) {
        sendResponse(false, null, 'Invalid email format');
    }

    $db = TenantManager::getDatabase();
    $checker = new AvailabilityChecker($db);

    // Get service details
    $service = $db->fetchRow(
        "SELECT * FROM services WHERE id = :id",
        [':id' => $serviceId]
    );

    if (!$service) {
        sendResponse(false, null, 'Service not found');
    }

    // Check availability
    if (!$checker->isTimeSlotAvailable($serviceId, $date, $time)) {
        sendResponse(false, null, 'Time slot is no longer available');
    }

    // Handle employee selection
    if ($employeeId) {
        if (!$checker->isEmployeeAvailableAtTime($employeeId, $date, $time, $service['duration'])) {
            sendResponse(false, null, 'Selected staff member is no longer available for this time slot');
        }
    } else {
        $employeeId = $checker->findAvailableEmployee($serviceId, $date, $time);
        if (!$employeeId) {
            sendResponse(false, null, 'No available staff for this time slot');
        }
    }

    // Start transaction
    $db->beginTransaction();

    try {
        // Check if customer exists
        $existingCustomer = $db->fetchRow(
            "SELECT * FROM customers WHERE email = :email",
            [':email' => $customerEmail]
        );

        if ($existingCustomer) {
            // Update existing customer with language
            $db->query(
                "UPDATE customers SET name = :name, phone = :phone, language = :language, updated_at = :updated
                 WHERE id = :id",
                [
                    ':name' => $customerName,
                    ':phone' => $customerPhone,
                    ':language' => $customerLanguage,
                    ':updated' => date('Y-m-d H:i:s'),
                    ':id' => $existingCustomer['id']
                ]
            );
            $customerId = $existingCustomer['id'];
        } else {
            // Create new customer with language
            $customerId = 'CUST' . uniqid();
            $db->query(
                "INSERT INTO customers (id, name, email, phone, language, created_at)
                 VALUES (:id, :name, :email, :phone, :language, :created)",
                [
                    ':id' => $customerId,
                    ':name' => $customerName,
                    ':email' => $customerEmail,
                    ':phone' => $customerPhone,
                    ':language' => $customerLanguage,
                    ':created' => date('Y-m-d H:i:s')
                ]
            );
        }

        // Create reservation
        $reservationId = 'RSV' . uniqid();
        $endTime = date('H:i', strtotime($time) + ($service['duration'] * 60));

        // Get verification method and booking confirmation setting
        $verificationMethod = $db->fetchColumn(
            "SELECT value FROM settings WHERE key = 'verification_method'",
            []
        ) ?: 'email';

        $bookingConfirmation = $db->fetchColumn(
            "SELECT value FROM settings WHERE key = 'booking_confirmation'",
            []
        ) ?: 'auto';

        // Determine reservation status based on verification method and booking confirmation
        if ($verificationMethod === 'none') {
            // No verification required
            $reservationStatus = ($bookingConfirmation === 'auto') ? 'confirmed' : 'pending';
        } else {
            // Verification required - always start as pending until verified
            $reservationStatus = 'pending';
        }

        $db->query(
            "INSERT INTO reservations (id, customer_id, service_id, employee_id, date,
             start_time, end_time, status, notes, price, created_at)
             VALUES (:id, :customer_id, :service_id, :employee_id, :date, :start_time,
             :end_time, :status, :notes, :price, :created)",
            [
                ':id' => $reservationId,
                ':customer_id' => $customerId,
                ':service_id' => $serviceId,
                ':employee_id' => $employeeId,
                ':date' => $date,
                ':start_time' => $time,
                ':end_time' => $endTime,
                ':status' => $reservationStatus,
                ':notes' => $customerNotes,
                ':price' => $service['price'],
                ':created' => date('Y-m-d H:i:s')
            ]
        );

        // Handle verification
        $businessName = $db->fetchColumn(
            "SELECT value FROM settings WHERE key = 'business_name'",
            []
        ) ?: 'Booking System';

        $verificationResult = ['success' => true, 'message' => ''];
        $verificationRequired = false;
        $verificationExpires = null;

        if ($verificationMethod !== 'none') {
            $verification = new VerificationManager($db);
            $verificationCode = $verification->generateCode('booking', $reservationId);

            if ($verificationMethod === 'email') {
                $mailer = new SMTPMailer();
                $verificationResult = [
                    'success' => $mailer->sendVerificationCode($customerEmail, $verificationCode, $businessName, $customerLanguage),
                    'message' => 'Verification code sent to email'
                ];
            }

            $verificationRequired = true;
            $verificationExpires = date('Y-m-d H:i:s', time() + 300); // 5 minutes
        }

        $db->commit();

        // Send admin notification for new reservation (always send for new bookings)
        try {
            // Get full reservation details for admin notification
            $reservationDetails = $db->fetchRow(
                "SELECT r.*, c.name as customer_name, c.email as customer_email, c.phone as customer_phone,
                 s.name as service_name, s.duration, e.name as employee_name
                 FROM reservations r
                 JOIN customers c ON r.customer_id = c.id
                 JOIN services s ON r.service_id = s.id
                 JOIN employees e ON r.employee_id = e.id
                 WHERE r.id = :id",
                [':id' => $reservationId]
            );

            if ($reservationDetails) {
                $mailer = new SMTPMailer();
                $mailer->sendAdminReservationNotification([
                    'reservation_id' => $reservationDetails['id'],
                    'customer_name' => $reservationDetails['customer_name'],
                    'customer_email' => $reservationDetails['customer_email'],
                    'customer_phone' => $reservationDetails['customer_phone'] ?? '',
                    'service_name' => $reservationDetails['service_name'],
                    'date' => $reservationDetails['date'],
                    'start_time' => $reservationDetails['start_time'],
                    'duration' => $reservationDetails['duration'],
                    'employee_name' => $reservationDetails['employee_name'],
                    'price' => $reservationDetails['price'],
                    'status' => $reservationDetails['status'],
                    'notes' => $reservationDetails['notes'] ?? '',
                    'created_at' => $reservationDetails['created_at']
                ]);
            }
        } catch (Exception $e) {
            // Don't fail the booking if admin notification fails
            error_log("Admin notification failed: " . $e->getMessage());
        }

        // Send confirmation email if booking is immediately confirmed
        if ($reservationStatus === 'confirmed') {
            try {
                sendConfirmationEmail([
                    'id' => $reservationId,
                    'customer_id' => $customerId,
                    'service_id' => $serviceId,
                    'employee_id' => $employeeId,
                    'date' => $date,
                    'start_time' => $time,
                    'status' => $reservationStatus
                ]);
            } catch (Exception $e) {
                error_log("Confirmation email failed: " . $e->getMessage());
            }
        }

        // Prepare response
        $responseData = [
            'reservation_id' => $reservationId,
            'verification_required' => $verificationRequired,
            'verification_method' => $verificationMethod,
            'customer_language' => $customerLanguage
        ];

        if ($verificationRequired) {
            $responseData['verification_expires'] = $verificationExpires;
            $responseData['message'] = $verificationResult['success']
                ? $verificationResult['message']
                : 'Booking created but verification failed: ' . $verificationResult['message'];
        } else {
            $responseData['message'] = 'Booking confirmed successfully!';
        }

        sendResponse(true, $responseData, 'Booking processed successfully');
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
} catch (Exception $e) {
    error_log("Booking API Error: " . $e->getMessage());
    sendResponse(false, null, 'Booking system temporarily unavailable. Please try again.');
}

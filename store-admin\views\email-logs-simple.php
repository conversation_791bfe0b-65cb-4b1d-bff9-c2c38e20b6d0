<?php

/**
 * Simple Email Logs View - Guaranteed to work
 */

// Include EmailLogger
require_once __DIR__ . '/../../shared/EmailLogger.php';

// Get database
$db = TenantManager::getDatabase();
$logger = new EmailLogger($db);

// Get email logs
try {
    $result = $logger->getEmailLogs([], 1, 50);
    $logs = $result['logs'];
    $total = $result['total'];
} catch (Exception $e) {
    $logs = [];
    $total = 0;
    $error = $e->getMessage();
}

?>

<div class="page-header">
    <h1><i class="fas fa-envelope-open-text"></i> Email Logs</h1>
    <p>View all emails sent from the system</p>
</div>

<?php if (isset($error)): ?>
    <div class="alert alert-danger">
        <strong>Error:</strong> <?= htmlspecialchars($error) ?>
    </div>
<?php endif; ?>

<div class="stats-card">
    <h3>Email Statistics</h3>
    <p><strong>Total Emails:</strong> <?= number_format($total) ?></p>
</div>

<div class="table-container">
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Sent At</th>
                <th>Customer</th>
                <th>Subject</th>
                <th>Type</th>
                <th>Language</th>
                <th>Status</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php if (empty($logs)): ?>
                <tr>
                    <td colspan="7" style="text-align: center; padding: 40px;">
                        <i class="fas fa-inbox" style="font-size: 48px; color: #ccc; margin-bottom: 16px;"></i>
                        <p>No email logs found</p>
                        <p style="color: #666;">Emails will appear here once they are sent from the system.</p>
                    </td>
                </tr>
            <?php else: ?>
                <?php foreach ($logs as $log): ?>
                    <tr>
                        <td>
                            <div>
                                <strong><?= date('M j, Y', strtotime($log['sent_at'])) ?></strong><br>
                                <small><?= date('H:i', strtotime($log['sent_at'])) ?></small>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong><?= htmlspecialchars($log['customer_name'] ?: $log['customer_full_name'] ?: 'Unknown') ?></strong><br>
                                <small><?= htmlspecialchars($log['customer_email']) ?></small>
                            </div>
                        </td>
                        <td>
                            <div style="max-width: 300px;">
                                <?= htmlspecialchars(mb_substr($log['subject'], 0, 60)) ?>
                                <?= mb_strlen($log['subject']) > 60 ? '...' : '' ?>
                            </div>
                        </td>
                        <td>
                            <span class="badge badge-<?= $log['email_type'] ?>">
                                <?= ucfirst(str_replace('_', ' ', $log['email_type'])) ?>
                            </span>
                        </td>
                        <td>
                            <span class="badge badge-secondary">
                                <?= strtoupper($log['language']) ?>
                            </span>
                        </td>
                        <td>
                            <span class="badge badge-<?= $log['status'] === 'sent' ? 'success' : 'danger' ?>">
                                <?= ucfirst($log['status']) ?>
                            </span>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-primary" onclick="viewEmail('<?= $log['id'] ?>')">
                                <i class="fas fa-eye"></i> View
                            </button>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<!-- Email View Modal -->
<div id="emailModal" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 800px;">
        <div class="modal-header">
            <h3><i class="fas fa-envelope-open"></i> Email Details</h3>
            <button class="modal-close" onclick="closeModal()">&times;</button>
        </div>
        <div class="modal-body" id="emailContent">
            <!-- Email content will be loaded here -->
        </div>
    </div>
</div>

<style>
.stats-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.table-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table {
    width: 100%;
    margin-bottom: 0;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 12px;
    border-bottom: 1px solid #dee2e6;
    vertical-align: top;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0,0,0,.05);
}

.badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 600;
    border-radius: 4px;
    text-transform: uppercase;
}

.badge-verification { background-color: #007bff; color: white; }
.badge-booking_confirmation { background-color: #28a745; color: white; }
.badge-custom { background-color: #6f42c1; color: white; }
.badge-secondary { background-color: #6c757d; color: white; }
.badge-success { background-color: #28a745; color: white; }
.badge-danger { background-color: #dc3545; color: white; }

.btn {
    display: inline-block;
    padding: 6px 12px;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.42857143;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: 4px;
    text-decoration: none;
}

.btn-primary {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 12px;
    line-height: 1.5;
    border-radius: 3px;
}

.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 0;
    border: none;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
}

.modal-header {
    padding: 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
}

.alert {
    padding: 12px 16px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}
</style>

<script>
function viewEmail(logId) {
    // Simple AJAX request to get email details
    fetch('/store-admin/controllers/ajax.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=get_email_log&log_id=' + logId
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showEmailModal(data.email);
        } else {
            alert('Failed to load email: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        alert('Error loading email: ' + error.message);
    });
}

function showEmailModal(email) {
    const content = document.getElementById('emailContent');
    content.innerHTML = `
        <div style="margin-bottom: 20px;">
            <h4>Email Information</h4>
            <table style="width: 100%; border-collapse: collapse;">
                <tr><td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>To:</strong></td><td style="padding: 8px; border-bottom: 1px solid #eee;">${email.customer_email}</td></tr>
                <tr><td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>Customer:</strong></td><td style="padding: 8px; border-bottom: 1px solid #eee;">${email.customer_name || 'Unknown'}</td></tr>
                <tr><td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>Subject:</strong></td><td style="padding: 8px; border-bottom: 1px solid #eee;">${email.subject}</td></tr>
                <tr><td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>Type:</strong></td><td style="padding: 8px; border-bottom: 1px solid #eee;">${email.email_type}</td></tr>
                <tr><td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>Language:</strong></td><td style="padding: 8px; border-bottom: 1px solid #eee;">${email.language}</td></tr>
                <tr><td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>Sent:</strong></td><td style="padding: 8px; border-bottom: 1px solid #eee;">${new Date(email.sent_at).toLocaleString()}</td></tr>
            </table>
        </div>
        <div>
            <h4>Email Content</h4>
            <div style="border: 1px solid #ddd; padding: 15px; background: #f9f9f9; max-height: 400px; overflow-y: auto;">
                ${email.content}
            </div>
        </div>
    `;
    
    document.getElementById('emailModal').style.display = 'block';
}

function closeModal() {
    document.getElementById('emailModal').style.display = 'none';
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('emailModal');
    if (event.target === modal) {
        closeModal();
    }
}
</script>
